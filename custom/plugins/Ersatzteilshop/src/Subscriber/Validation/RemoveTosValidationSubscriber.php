<?php declare(strict_types=1);

namespace Ersatzteilshop\Subscriber\Validation;

use Shopware\Core\Framework\Validation\BuildValidationEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Alternative approach: Remove TOS validation via event subscriber
 * This is a backup solution if the decorator doesn't work
 * Currently disabled - using decorator approach instead
 */
class RemoveTosValidationSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            'order.create' => 'removeTosValidation',
        ];
    }

    public function removeTosValidation(BuildValidationEvent $event): void
    {
        $definition = $event->getDefinition();
        
        // Remove TOS validation constraint
        $definition->set('tos', null);
    }
}
