<?php declare(strict_types=1);

namespace Ersatzteilshop\Subscriber\Validation;

use Shopware\Core\Framework\Validation\BuildValidationEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Alternative approach: Remove TOS validation via event subscriber
 * This is a backup solution if the decorator doesn't work
 * Currently disabled - using decorator approach instead
 */
class RemoveTosValidationSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            'order.create' => 'removeTosValidation',
        ];
    }

    public function removeTosValidation(BuildValidationEvent $event): void
    {
        $definition = $event->getDefinition();

        // Remove TOS validation constraint by accessing the properties directly
        $properties = $definition->getProperties();
        if (isset($properties['tos'])) {
            // Use reflection to access the private properties array and remove the tos validation
            $reflection = new \ReflectionClass($definition);
            $propertiesProperty = $reflection->getProperty('properties');
            $propertiesProperty->setAccessible(true);
            $currentProperties = $propertiesProperty->getValue($definition);
            unset($currentProperties['tos']);
            $propertiesProperty->setValue($definition, $currentProperties);
        }
    }
}
