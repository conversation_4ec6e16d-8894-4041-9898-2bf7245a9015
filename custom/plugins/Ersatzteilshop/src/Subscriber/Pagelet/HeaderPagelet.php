<?php declare(strict_types=1);

namespace Ersatzteilshop\Subscriber\Pagelet;

use Shopware\Core\Content\Category\CategoryCollection;
use Shopware\Core\Content\Category\Service\NavigationLoaderInterface;
use Shopware\Core\Content\Category\Tree\TreeItem;
use Shopware\Core\Framework\Context;
use Shopware\Core\SalesChannelRequest;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Ersatzteilshop\Service\CachedMenuService;
use Shopware\Core\Framework\Struct\ArrayStruct;
use Shopware\Storefront\Theme\ThemeService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Shopware\Storefront\Pagelet\Header\HeaderPageletLoadedEvent;

class HeaderPagelet implements EventSubscriberInterface
{

    public const MENU_CATEGORIES = ['household', 'kitchen', 'emobility'];

    public const SUPPORTED_MENU
        = [ 'cmsCategoriesOverview'    => self::CMS_CATEGORIES_OVERVIEW ];

    private const CMS_CATEGORIES_OVERVIEW
        = [
            'key'      => CachedMenuService::ROOT_NAVIGATION_KEY,
            'template' => '@Ersatzteilshop/storefront/element/component/categories-overview.html.twig',
            'depth'    => 2,
        ];




    public function __construct(
        private CachedMenuService $cachedMenuService,
        private NavigationLoaderInterface $navigationLoader,
        private SystemConfigService $systemConfigService,
        private ThemeService      $themeService,
    ) {}

    public static function getSubscribedEvents()
    {
        return [
            HeaderPageletLoadedEvent::class => [
                ['onHeaderPageletLoaded' ,20],
                ['loadCategoryOverview' ,200],
            ]
        ];
    }

    public function onHeaderPageletLoaded(HeaderPageletLoadedEvent $event): void
    {

        $context = clone $event->getContext();
        $context->addState(Context::STATE_ELASTICSEARCH_AWARE);

        foreach (self::MENU_CATEGORIES as $menuCategory) {
            $menu = $this->getMenuByCategory($menuCategory, $event->getSalesChannelContext());
            $event->getPagelet()->addExtension(
                "{$menuCategory}Menu",
                new ArrayStruct(['menu' => $menu])
            );
        }
    }

    private function getMenuByCategory(string $category, SalesChannelContext $context): CategoryCollection
    {
        $categoryId = $this->systemConfigService->get("Ersatzteilshop.config.{$category}Category");
        $navigation = $this->navigationLoader->load($categoryId, $context, $categoryId, 1);

        return new CategoryCollection(array_map(static function (TreeItem $treeItem) {
            return $treeItem->getCategory();
        }, $navigation->getTree()));
    }

    /**
     * @param HeaderPageletLoadedEvent $event
     */
    public function loadCategoryOverview(HeaderPageletLoadedEvent $event): void
    {
        $themeId = $event->getRequest()->attributes->get(SalesChannelRequest::ATTRIBUTE_THEME_ID);

        $context = clone $event->getContext();
        $context->addState(Context::STATE_ELASTICSEARCH_AWARE);

        $themeConfig = $this->getThemeConfig($themeId, $context);

        foreach (self::SUPPORTED_MENU as $name => $config) {
            $navigationId = $themeConfig['fields'][$config['key']]['value'] ?? $config['key'];
            $event->getPagelet()->addExtension(
                $name,
                new ArrayStruct(
                    [
                        'html' => $this->cachedMenuService->get(
                            $config['template'],
                            $navigationId,
                            $config['depth'],
                            $event->getSalesChannelContext()
                        ),
                    ]
                )
            );
        }
    }

    /**
     * @param $themeId
     * @param $context
     *
     * @return array
     */
    private function getThemeConfig($themeId, $context): array
    {
        return $this->themeService->getThemeConfiguration($themeId, false, $context);
    }

}