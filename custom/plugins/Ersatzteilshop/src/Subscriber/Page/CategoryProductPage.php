<?php declare(strict_types=1);

namespace Ersatzteilshop\Subscriber\Page;

use Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerCollection;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\Uuid\Uuid;
use Ersatzteilshop\Service\CategoryLoader;
use Ersatzteilshop\Service\ManufacturerLoader;
use Ersatzteilshop\Service\ResourceRuleLoader;
use Ersatzteilshop\Service\ProductReviewLoader;
use Ersatzteilshop\Util\ProductPrioritySorting;
use Ersatzteilshop\Event\CategoryPageLoadedEvent;
use Ersatzteilshop\Storefront\Page\Category\CategoryPage;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Ersatzteilshop\Core\Content\RepairGuide\RepairGuideCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Ersatzteilshop\Core\Content\ResourceRule\ResourceRuleDefinition;
use Shopware\Core\Content\Product\SalesChannel\ProductAvailableFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\OrFilter;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepositoryInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepositoryInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Ersatzteilshop\Core\Content\Product\Sorting\ShadowProductShownLastSorting;
use Shopware\Core\Content\Product\SalesChannel\Listing\AbstractProductListingRoute;
use Shopware\Core\Content\Product\Aggregate\ProductVisibility\ProductVisibilityDefinition;
use Ersatzteilshop\Core\Content\ProductManufacturer\AbstractProductManufacturerOverviewRoute;

class CategoryProductPage implements EventSubscriberInterface
{
    public function __construct(
        private EntityRepositoryInterface                $categoryRepository,
        private EntityRepositoryInterface                $manufacturerRepository,
        private SalesChannelRepositoryInterface          $productRepository,
        private AbstractProductListingRoute              $productListingRoute,
        private CategoryLoader                           $categoryLoader,
        private ProductReviewLoader                      $productReviewLoader,
        private ManufacturerLoader                       $manufacturerLoader,
        private EntityRepositoryInterface                $categoryManufacturerRepository,
        private ResourceRuleLoader                       $resourceRuleLoader,
        private SystemConfigService                      $configService,
        private AbstractProductManufacturerOverviewRoute $productManufacturerOverviewRoute
    )
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CategoryPageLoadedEvent::class => [
                ['getCategoryProductData', 20],
            ]
        ];
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getCategoryProductData(CategoryPageLoadedEvent $event): void
    {
        if ($event->getPage()->getCmsPage()) {
            return;
        }

        $this->activeCategory = $event->getPage()->getCategory();

        if (!in_array($this->activeCategory->getType(), [CategoryPage::TYPE_PRODUCT, CategoryPage::TYPE_TOOLS, CategoryPage::TYPE_CLEANING])) {
            return;
        }

        // render sidebar
        if ($this->activeCategory->getChildCount()) {
           $this->getChildrenCategories($event);
        }
        $productListFlag = $this->activeCategory->getTranslation('customFields')['product_list'] ?? false;

        $this->getParentCategory($event);
        $this->getSearchWidgetVideo($event);
        $tags = $this->getTags($event, $this->activeCategory);#
        if($tags !== null) {
            $this->activeCategory->setTags($tags);
        }
        if ($productListFlag || $this->activeCategory->getLevel() > 3) {
            $this->getProductList($event);
            $this->getRepairProduct($event);
            $this->getTopManufacturers($event);
        } else {
            $this->getVideos($event);
            $this->getTopCategories($event);
            $this->getFeaturedProducts($event);
            $this->getRepairGuides($event);
            $this->getRepairInstructions($event);
            $this->getRepairInstructionStartPage($event);
            $this->getBlogs($event, $this->activeCategory);

            $categoryPath = $this->activeCategory->getPath();

            // Exclude E-Mobilität Categories
            if (!str_contains($categoryPath, '58f9cd03dc874fd3bd121c540be1e9d2')) {
                $this->getCleaningProducts($event);
            }

            $this->getTopManufacturers($event);
        }
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getChildrenCategories(CategoryPageLoadedEvent $event): void
    {
        $request = $event->getRequest();
        $manufacturerId = $request->get('manufacturer');

        $childrenCategories = $this->categoryLoader->loadChildrenCategories(
            $this->activeCategory->getId(),
            $this->activeCategory->getLevel() + 1,
            $event->getSalesChannelContext()
        );

        if ($manufacturerId) {
            $childrenCategories = $this->categoryLoader->filterByManufacturer(
                $childrenCategories,
                $manufacturerId,
                $event->getSalesChannelContext()
            );
        }

        $event->getPage()->addExtension('childrenCategories', $childrenCategories);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getProductList(CategoryPageLoadedEvent $event) : void
    {
        $criteria = new Criteria();

        $searchResult = $this->productListingRoute->load(
            $this->activeCategory->getId(),
            $event->getRequest(),
            $event->getSalesChannelContext(),
            $criteria
        )->getResult();

        $this->productReviewLoader->load(
            $searchResult->getEntities(),
            $event->getSalesChannelContext()->getContext()
        );
        $event->getPage()->addExtension('products', $searchResult);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getRepairProduct(CategoryPageLoadedEvent $event) : void
    {
        $idValue = $this->configService->get('Ersatzteilshop.config.repairItemId') ?? '';
        if(!Uuid::isValid($idValue)) {
            return;
        }
        $criteria = new Criteria([$idValue]);
        $criteria->addAssociation('properties');
        $repairProduct = $this->productRepository->search($criteria, $event->getSalesChannelContext())->first();
        $event->getPage()->addExtension('repairProduct', $repairProduct);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getTopCategories(CategoryPageLoadedEvent $event)
    {
        $request        = $event->getRequest();
        $manufacturerId = $request->get('manufacturer');

        $topCategories = $this->categoryLoader->loadTopCategories(
            $this->activeCategory->getId(),
            $manufacturerId,
            12,
            $event->getSalesChannelContext());
        $event->getPage()->addExtension('topCategories', $topCategories);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getFeaturedProducts(CategoryPageLoadedEvent $event)
    {
        $request        = $event->getRequest();
        $manufacturerId = $request->get('manufacturer');

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('product.categoriesRo.id', $this->activeCategory->getId()));

        if ($manufacturerId) {
            $queries = [
                new EqualsFilter('product.manufacturerId', $manufacturerId),
                new EqualsFilter('product.appliances.manufacturerId', $manufacturerId),
            ];

            $criteria->addFilter(new OrFilter($queries));
        }

        $criteria->addAssociation('properties');

        $criteria->addFilter(
            new ProductAvailableFilter($event->getSalesChannelContext()->getSalesChannel()->getId(), ProductVisibilityDefinition::VISIBILITY_ALL)
        );

        $criteria->addAssociation('cover');
        $criteria->addAssociation('manufacturer');
        ProductPrioritySorting::scoreQuery($criteria);
        $criteria->setLimit(10);

        $context = clone $event->getSalesChannelContext();
        $context->addState(Context::STATE_ELASTICSEARCH_AWARE);

        $products = $this->productRepository->search($criteria, $context)->getEntities();
        $event->getPage()->addExtension('featuredProducts', $products);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getParentCategory(CategoryPageLoadedEvent $event)
    {
        $criteria = new Criteria([$this->activeCategory->getParentId()]);
        $criteria->addAssociation('media');

        $parentCategory = $this->categoryRepository->search(
            $criteria,
            $event->getSalesChannelContext()->getContext()
        )->getEntities()->first();

        $event->getPage()->addExtension('parentCategory', $parentCategory);
        $event->getPage()->getCategory()->setParent($parentCategory);
        $event->getPage()->getCategoryManufacturer()?->getCategory()?->setParent($parentCategory);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getRepairGuides(CategoryPageLoadedEvent $event)
    {
        $repairGuides = $this->resourceRuleLoader->loadRepairGuides(
            ResourceRuleDefinition::MAPPING_TYPE_CATEGORY_PRODUCT,
            $this->activeCategory,
            $event->getSalesChannelContext());

        if ($repairGuides instanceof RepairGuideCollection) {
            $repairGuides = $repairGuides->filterVisible();
            $repairGuides = $repairGuides->slice(0, 6);
        }

        $event->getPage()->addExtension('repairGuides', $repairGuides);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getVideos(CategoryPageLoadedEvent $event): void
    {
        $videos = $this->resourceRuleLoader->loadVideoLibraries(ResourceRuleDefinition::MAPPING_TYPE_CATEGORY_PRODUCT,
            $this->activeCategory,
            $event->getSalesChannelContext(),
            10
        );

        $event->getPage()->addExtension('videos', $videos);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getSearchWidgetVideo(CategoryPageLoadedEvent $event)
    {
        $video = $this->resourceRuleLoader->loadSearchWidgetVideo(ResourceRuleDefinition::MAPPING_TYPE_SEARCH_WIDGET_VIDEO,
            $this->activeCategory,
            $event->getSalesChannelContext(),
            1
        );
        $event->getPage()->addExtension('searchWidgetVideo', $video);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getRepairInstructions(CategoryPageLoadedEvent $event)
    {
        $repairInstructions = $this->resourceRuleLoader
            ->loadRepairInstructions(ResourceRuleDefinition::MAPPING_TYPE_CATEGORY_PRODUCT,
                                     $this->activeCategory,
                                     $event->getSalesChannelContext(),
                                     10
            );


        $repairInstructions->getIds();

        $event->getPage()->addExtension('repairInstructions', $repairInstructions);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    private function getRepairInstructionStartPage(CategoryPageLoadedEvent $event)
    {
        $repairInstructionStartPage = $this->categoryLoader
            ->loadRepairInstructionStartPage($event->getSalesChannelContext());

        $event->getPage()->addExtension('repairInstructionStarPage', $repairInstructionStartPage);

    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getBlogs(CategoryPageLoadedEvent $event, $activeCategory)
    {
        $criteria = new Criteria();
        $criteria->addAssociation('blogs');
        $criteria->addAssociation('tags');
        $criteria->addFilter(new EqualsFilter('category.id', $activeCategory->getId()));
        $criteria->setLimit(1);

        $category = $this->categoryRepository->search($criteria, $event->getContext())->first();
        if (null === $category) {
            return;
        }

        $filterTag = null;
        if ($category->getTags()->count() > 0) {
            $tags = $category->getTags();
            foreach ($tags as $tag) {
                $tag = $tag->getName();
                if($tag === 'kitchen'){
                    $filterTag = 'kitchen';
                }
                if($tag === 'household'){
                    $filterTag = 'household';
                }
            }
        }
        if ($category && $filterTag) {
            $criteria = new Criteria();
            $criteria->addAssociation('blogs');
            $criteria->addFilter(new OrFilter(
                [
                    new ContainsFilter('tags.name', $filterTag),
                    new ContainsFilter('path', '|' . $category->getId() . '|'),
                    new EqualsFilter('id', $category->getId())
                ]
            ));

            $categories = $this->categoryRepository->search(
                $criteria,
                $event->getContext()
            );
        } else {
            $categories = [];
        }

        $blogs = [];
        // Get blogs for activeCategory
        if (empty($categories)) {
            $categories[] = $category;
        }
        foreach ($categories as $category) {
            foreach ($category->getExtensions()['blogs'] as $blog) {
                $blogs[] = $blog;
            }
        }

        $blogs = array_unique($blogs, SORT_REGULAR);
        $blogsCollection = new EntityCollection();
        foreach ($blogs as $blog) {
            $blogsCollection->add($blog);
        }
        $event->getPage()->addExtension('blogs', $blogsCollection);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getCleaningProducts(CategoryPageLoadedEvent $event)
    {
        $idValue = $this->configService->get('Ersatzteilshop.config.sliderID') ?? '';
        $criteria = new Criteria();
        $criteria->addAssociation('properties');
        $criteria->addAssociation('productStream');

        $criteria->addFilter(new EqualsFilter('streamIds', $idValue));
        $criteria->addAssociation('cover');
        ProductPrioritySorting::scoreQuery($criteria);

        $context = clone $event->getSalesChannelContext();
        $context->addState(Context::STATE_ELASTICSEARCH_AWARE);

        $products = $this->productRepository->search($criteria, $context)->getEntities();
        $event->getPage()->addExtension('cleaningProducts', $products);
    }

    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getAllManufacturers(CategoryPageLoadedEvent $event)
    {
        $manufacturers = $this->productManufacturerOverviewRoute->loadManufacturers($event->getSalesChannelContext());
        $event->getPage()->addExtension('manufacturers', $manufacturers);
    }


    /**
     * @param CategoryPageLoadedEvent $event
     */
    public function getTags(CategoryPageLoadedEvent $event, $activeCategory)
    {
        $criteria = new Criteria();
        $criteria->addAssociation('tags');
        $criteria->addFilter(new EqualsFilter('category.id', $activeCategory->getId()));
        $criteria->setLimit(1);

        $category = $this->categoryRepository->search($criteria, $event->getContext())->first();
        if (null === $category) {
            return null;
        }

        $filterTag = null;
        if ($category->getTags()->count() > 0) {
            return $category->getTags();
        }
        return null;
    }

    private function getTopManufacturers(CategoryPageLoadedEvent $event): void
    {
        $topManufacturers = $this->productManufacturerOverviewRoute->loadManufacturersByProductCount(
            $event->getPage()->getCategory()->getId(),
            10000,
            $event->getSalesChannelContext()
        );

        $manufacturersArray = iterator_to_array($topManufacturers);
        $first15 = array_slice($manufacturersArray, 0, 15);
        $rest = array_slice($manufacturersArray, 15);

        usort($first15, function ($a, $b) {
            return strcmp($a->getName(), $b->getName());
        });

        $sortedManufacturersArray = array_merge($first15, $rest);
        $sortedManufacturers = new ProductManufacturerCollection($sortedManufacturersArray);

        $event->getPage()->addExtension(
            'topManufactures',
            $sortedManufacturers
        );
    }
}
