<?php declare(strict_types=1);

namespace Ersatzteilshop\Decorator\Core\Checkout\Order\Validation;

use Shopware\Core\Checkout\Order\Validation\OrderValidationFactory;
use Shopware\Core\Framework\Validation\DataValidationDefinition;
use Shopware\Core\Framework\Validation\DataValidationFactoryInterface;
use Shopware\Core\System\SalesChannel\SalesChannelContext;

/**
 * Decorator to remove TOS validation from order creation
 */
class OrderValidationFactoryDecorator implements DataValidationFactoryInterface
{
    private DataValidationFactoryInterface $decorated;

    public function __construct(DataValidationFactoryInterface $decorated)
    {
        $this->decorated = $decorated;
    }

    public function create(SalesChannelContext $context): DataValidationDefinition
    {
        $definition = $this->decorated->create($context);
        
        // Remove TOS validation - no checkbox required
        $definition->set('tos', null);
        
        return $definition;
    }

    public function update(SalesChannelContext $context): DataValidationDefinition
    {
        return $this->decorated->update($context);
    }
}
