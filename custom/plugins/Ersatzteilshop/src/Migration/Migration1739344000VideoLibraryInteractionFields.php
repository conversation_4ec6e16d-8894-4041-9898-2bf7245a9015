<?php declare(strict_types=1);

namespace Ersatzteilshop\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Migration\MigrationStep;
use Shopware\Core\Framework\Uuid\Uuid;

class Migration1739344000VideoLibraryInteractionFields extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1739344000;
    }

    public function update(Connection $connection): void
    {
        $date = (new \DateTime())->format(Defaults::STORAGE_DATE_TIME_FORMAT);

        $existingSet = $connection->fetchAssociative(
            'SELECT id FROM custom_field_set WHERE name = :name',
            ['name' => 'ersatzteilshop_video_library']
        );

        if (!$existingSet) {
            return;
        }

        $setId = $existingSet['id'];

        $likesFieldExists = $connection->fetchAssociative(
            'SELECT id FROM custom_field WHERE name = :name',
            ['name' => 'ersatzteilshop_video_library_likes']
        );

        if (!$likesFieldExists) {
            $connection->insert('custom_field', [
                'id' => Uuid::randomBytes(),
                'name' => 'ersatzteilshop_video_library_likes',
                'type' => 'int',
                'config' => json_encode([
                    'label' => [
                        'en-GB' => 'Likes',
                        'de-DE' => 'Likes',
                    ],
                    'customFieldType' => 'number',
                    'customFieldPosition' => 1,
                ]),
                'set_id' => $setId,
                'created_at' => $date
            ]);
        }

        $sharesFieldExists = $connection->fetchAssociative(
            'SELECT id FROM custom_field WHERE name = :name',
            ['name' => 'ersatzteilshop_video_library_shares']
        );

        if (!$sharesFieldExists) {
            $connection->insert('custom_field', [
                'id' => Uuid::randomBytes(),
                'name' => 'ersatzteilshop_video_library_shares',
                'type' => 'int',
                'config' => json_encode([
                    'label' => [
                        'en-GB' => 'Shares',
                        'de-DE' => 'Shares',
                    ],
                    'customFieldType' => 'number',
                    'customFieldPosition' => 2,
                ]),
                'set_id' => $setId,
                'created_at' => $date
            ]);
        }

        $videoRequestsFieldExists = $connection->fetchAssociative(
            'SELECT id FROM custom_field WHERE name = :name',
            ['name' => 'ersatzteilshop_video_library_video_requests']
        );

        if (!$videoRequestsFieldExists) {
            $connection->insert('custom_field', [
                'id' => Uuid::randomBytes(),
                'name' => 'ersatzteilshop_video_library_video_requests',
                'type' => 'int',
                'config' => json_encode([
                    'label' => [
                        'en-GB' => 'Video Requests',
                        'de-DE' => 'Video-Anfragen',
                    ],
                    'customFieldType' => 'number',
                    'customFieldPosition' => 3,
                ]),
                'set_id' => $setId,
                'created_at' => $date
            ]);
        }
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}