<?php declare(strict_types=1);

namespace Ersatzteilshop\Command;

use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\NotFilter;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class FixCategoryLinksCommand extends Command
{
    protected static $defaultName = 'eshop-commands:fix:category-links';
    private const CATEGORY_CUSTOM_FIELD_KEY = 'category_seo_desc';
    private const SALES_CHANNEL_ID = 'ec46d71abadf482c907cd9c79f8a4bdc';

    private EntityRepository $categoryRepository;
    private EntityRepository $categoryTranslationRepository;
    private EntityRepository $categoryManufacturerTranslationRepository;
    private EntityRepository $seoUrlRepository;

    public function __construct(
        EntityRepository $categoryRepository,
        EntityRepository $categoryTranslationRepository,
        EntityRepository $categoryManufacturerTranslationRepository,
        EntityRepository $seoUrlRepository
    ) {
        parent::__construct();
        $this->categoryRepository = $categoryRepository;
        $this->categoryTranslationRepository = $categoryTranslationRepository;
        $this->categoryManufacturerTranslationRepository = $categoryManufacturerTranslationRepository;
        $this->seoUrlRepository = $seoUrlRepository;
    }

    protected function configure(): void
    {
        $this
            ->addOption('language-id', 'l', InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY)
            ->addOption('category-id', 'c', InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY)
            ->addOption('manufacturer-id', 'm', InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY)
            ->addOption('dry-run', null, InputOption::VALUE_NONE)
            ->addOption('export-links', null, InputOption::VALUE_NONE);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $symfonyStyle = new SymfonyStyle($input, $output);
        $context = Context::createDefaultContext();
        $languageIds = $input->getOption('language-id');
        $categoryIds = $input->getOption('category-id');
        $manufacturerIds = $input->getOption('manufacturer-id');
        $isDryRun = (bool)$input->getOption('dry-run');
        $shouldExport = (bool)$input->getOption('export-links');

        if ($shouldExport) {
            $this->exportCategoryLinks($context, $languageIds, $categoryIds, $manufacturerIds, $symfonyStyle);
            return Command::SUCCESS;
        }

        $totalChecked = 0;
        $totalUpdated = 0;
        $totalErrors = 0;

        $totalChecked += $this->processCategoryTranslations(
            $context,
            $languageIds,
            $categoryIds,
            $isDryRun,
            $totalUpdated,
            $totalErrors
        );

        $totalChecked += $this->processCategoryManufacturerTranslations(
            $context,
            $languageIds,
            $manufacturerIds,
            $isDryRun,
            $totalUpdated,
            $totalErrors
        );

        $symfonyStyle->table(
            ['Metric', 'Value'],
            [
                ['Total Checked', $totalChecked],
                [$isDryRun ? 'Would Update' : 'Updated', $totalUpdated],
                ['Errors', $totalErrors],
            ]
        );

        return Command::SUCCESS;
    }

    private function getActiveCategoryIds(Context $context, array $restrictToIds = []): array
    {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('active', true));

        if (!empty($restrictToIds)) {
            $criteria->addFilter(new EqualsAnyFilter('id', $restrictToIds));
        }

        return $this->categoryRepository->searchIds($criteria, $context)->getIds();
    }

    private function processCategoryTranslations(
        Context $context,
        array $languageIds,
        array $categoryIds,
        bool $isDryRun,
        int &$totalUpdated,
        int &$totalErrors
    ): int {
        $criteria = new Criteria();
        if (!empty($languageIds)) {
            $criteria->addFilter(new EqualsAnyFilter('languageId', $languageIds));
        }
        if (!empty($categoryIds)) {
            $criteria->addFilter(new EqualsAnyFilter('categoryId', $categoryIds));
        }
        $criteria->addFilter(
            new MultiFilter(MultiFilter::CONNECTION_OR, [
                new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('description', null)]),
                new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('customFields', null)]),
            ])
        );

        $searchResult = $this->categoryTranslationRepository->search($criteria, $context);
        $updatePayload = [];

        foreach ($searchResult->getEntities() as $translationEntity) {
            $singleUpdateData = [
                'categoryId' => $translationEntity->getCategoryId(),
                'languageId' => $translationEntity->getLanguageId(),
            ];
            $requiresUpdate = false;

            $fixedDescription = $this->fixHtml($translationEntity->getDescription());
            if ($fixedDescription !== null) {
                $singleUpdateData['description'] = $fixedDescription;
                $requiresUpdate = true;
            }

            $customFields = $translationEntity->getCustomFields();
            if (is_array($customFields) && isset($customFields[self::CATEGORY_CUSTOM_FIELD_KEY])) {
                $fixedCustomFieldValue = $this->fixHtml($customFields[self::CATEGORY_CUSTOM_FIELD_KEY]);
                if ($fixedCustomFieldValue !== null) {
                    $customFields[self::CATEGORY_CUSTOM_FIELD_KEY] = $fixedCustomFieldValue;
                    $singleUpdateData['customFields'] = $customFields;
                    $requiresUpdate = true;
                }
            }

            if ($requiresUpdate) {
                $updatePayload[] = $singleUpdateData;
            }
        }

        if (!empty($updatePayload)) {
            $totalUpdated += count($updatePayload);
            if (!$isDryRun) {
                try {
                    $this->categoryTranslationRepository->update($updatePayload, $context);
                } catch (\Throwable $exception) {
                    $totalErrors += count($updatePayload);
                }
            }
        }

        return $searchResult->getTotal();
    }

    private function processCategoryManufacturerTranslations(
        Context $context,
        array $languageIds,
        array $manufacturerIds,
        bool $isDryRun,
        int &$totalUpdated,
        int &$totalErrors
    ): int {
        $criteria = new Criteria();
        $criteria->addAssociation('categoryManufacturer');

        if (!empty($languageIds)) {
            $criteria->addFilter(new EqualsAnyFilter('languageId', $languageIds));
        }
        if (!empty($manufacturerIds)) {
            $criteria->addFilter(new EqualsAnyFilter('categoryManufacturer.manufacturerId', $manufacturerIds));
        }
        $criteria->addFilter(
            new MultiFilter(MultiFilter::CONNECTION_OR, [
                new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('shortDescription', null)]),
                new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('longDescription', null)]),
            ])
        );

        $searchResult = $this->categoryManufacturerTranslationRepository->search($criteria, $context);
        $updatePayload = [];

        foreach ($searchResult->getEntities() as $translationEntity) {
            $singleUpdateData = [
                'categoryManufacturerId' => $translationEntity->getCategoryManufacturerId(),
                'languageId'             => $translationEntity->getLanguageId(),
            ];
            $requiresUpdate = false;

            $fixedShortDescription = $this->fixHtml($translationEntity->getShortDescription());
            if ($fixedShortDescription !== null) {
                $singleUpdateData['shortDescription'] = $fixedShortDescription;
                $requiresUpdate = true;
            }

            $fixedLongDescription = $this->fixHtml($translationEntity->getLongDescription());
            if ($fixedLongDescription !== null) {
                $singleUpdateData['longDescription'] = $fixedLongDescription;
                $requiresUpdate = true;
            }

            if ($requiresUpdate) {
                $updatePayload[] = $singleUpdateData;
            }
        }

        if (!empty($updatePayload)) {
            $totalUpdated += count($updatePayload);
            if (!$isDryRun) {
                try {
                    $this->categoryManufacturerTranslationRepository->update($updatePayload, $context);
                } catch (\Throwable $exception) {
                    $totalErrors += count($updatePayload);
                }
            }
        }

        return $searchResult->getTotal();
    }

    private function buildCanonicalMap(Context $context, array $languageIds, array $paths): array
    {
        $uniquePaths = array_values(array_unique($paths));
        $canonicalMap = [];

        foreach (array_chunk($uniquePaths, 1000) as $pathChunk) {
            $criteria = (new Criteria())
                ->addFilter(new EqualsAnyFilter('seoPathInfo', $pathChunk))
                ->addFilter(new EqualsFilter('isCanonical', true))
                ->addFilter(new EqualsFilter('salesChannelId', self::SALES_CHANNEL_ID));

            if (!empty($languageIds)) {
                $criteria->addFilter(new EqualsAnyFilter('languageId', $languageIds));
            }

            foreach ($this->seoUrlRepository->search($criteria, $context)->getEntities() as $seoUrlEntity) {
                $canonicalMap[strtolower($seoUrlEntity->getSeoPathInfo())] = true;
            }
        }

        return $canonicalMap;
    }

    private function exportCategoryLinks(
        Context $context,
        array $languageIds,
        array $categoryIds,
        array $manufacturerIds,
        SymfonyStyle $symfonyStyle
    ): void {
        $foundLinks = [];
        $activeCategoryIds = $this->getActiveCategoryIds($context, $categoryIds);

        $categoryCriteria = new Criteria();
        $categoryCriteria->addFilter(new EqualsAnyFilter('categoryId', $activeCategoryIds));
        if (!empty($languageIds)) {
            $categoryCriteria->addFilter(new EqualsAnyFilter('languageId', $languageIds));
        }
        $categoryCriteria->addFilter(
            new MultiFilter(MultiFilter::CONNECTION_OR, [
                new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('description', null)]),
                new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('customFields', null)]),
            ])
        );

        foreach ($this->categoryTranslationRepository->search($categoryCriteria, $context)->getEntities() as $translationEntity) {
            $links = array_merge(
                $this->grabLinks($translationEntity->getDescription()),
                $this->grabLinks($translationEntity->getCustomFields()[self::CATEGORY_CUSTOM_FIELD_KEY] ?? null)
            );
            if (!empty($links)) {
                $foundLinks['cat:' . $translationEntity->getCategoryId()] = array_unique($links);
            }
        }

        $manufacturerCriteria = new Criteria();
        $manufacturerCriteria->addAssociation('categoryManufacturer');
        if (!empty($languageIds)) {
            $manufacturerCriteria->addFilter(new EqualsAnyFilter('languageId', $languageIds));
        }
        if (!empty($manufacturerIds)) {
            $manufacturerCriteria->addFilter(new EqualsAnyFilter('categoryManufacturer.manufacturerId', $manufacturerIds));
        }
        $manufacturerCriteria->addFilter(
            new MultiFilter(MultiFilter::CONNECTION_OR, [
                new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('shortDescription', null)]),
                new NotFilter(MultiFilter::CONNECTION_AND, [new EqualsFilter('longDescription', null)]),
            ])
        );

        foreach ($this->categoryManufacturerTranslationRepository->search($manufacturerCriteria, $context)->getEntities() as $translationEntity) {
            $links = array_merge(
                $this->grabLinks($translationEntity->getShortDescription()),
                $this->grabLinks($translationEntity->getLongDescription())
            );
            if (!empty($links)) {
                $foundLinks['manu:' . $translationEntity->getCategoryManufacturerId()] = array_unique($links);
            }
        }

        if (empty($foundLinks)) {
            $symfonyStyle->writeln('No internal links found.');
            return;
        }

        $allPaths = [];
        foreach ($foundLinks as $linksGroup) {
            foreach ($linksGroup as $link) {
                $path = strtolower(ltrim(parse_url($link, PHP_URL_PATH) ?? '', '/'));
                if ($path !== '') {
                    $allPaths[] = $path;
                }
            }
        }
        $canonicalMap = $this->buildCanonicalMap($context, $languageIds, $allPaths);

        $timestamp = date('Y-m-d_H-i-s');
        $csvFilename = 'non_canonical_links_' . $timestamp . '.csv';
        $fileHandle = fopen($csvFilename, 'w');
        fputcsv($fileHandle, ['Entity', 'Link']);

        $totalLinks = 0;
        $nonCanonicalCount = 0;

        foreach ($foundLinks as $entityKey => $linksGroup) {
            foreach ($linksGroup as $link) {
                $totalLinks++;
                $path = strtolower(ltrim(parse_url($link, PHP_URL_PATH) ?? '', '/'));
                if (!isset($canonicalMap[$path])) {
                    $nonCanonicalCount++;
                    fputcsv($fileHandle, [$entityKey, $link]);
                }
            }
        }

        fclose($fileHandle);

        $symfonyStyle->table(
            ['Metric', 'Value'],
            [
                ['Entities with Links', count($foundLinks)],
                ['Total Links', $totalLinks],
                ['Non-Canonical', $nonCanonicalCount],
                ['File', $csvFilename],
            ]
        );
    }

    private function grabLinks(?string $htmlContent): array
    {
        if (trim((string)$htmlContent) === '') {
            return [];
        }

        libxml_use_internal_errors(true);
        $document = new \DOMDocument();
        $loaded = $document->loadHTML('<?xml encoding="utf-8" ?>' . $htmlContent, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();

        if (!$loaded) {
            return [];
        }

        $links = [];
        foreach ($document->getElementsByTagName('a') as $anchor) {
            if (!$anchor->hasAttribute('href')) {
                continue;
            }
            $href = trim($anchor->getAttribute('href'));
            if ($href === '' || str_starts_with($href, '//') || strpos($href, ':') !== false || $href[0] !== '/') {
                continue;
            }
            $links[] = $href;
        }

        return $links;
    }

    private function fixHtml(?string $htmlContent): ?string
    {
        if (trim((string)$htmlContent) === '') {
            return null;
        }

        libxml_use_internal_errors(true);
        $document = new \DOMDocument();
        $loaded = $document->loadHTML('<?xml encoding="utf-8" ?>' . $htmlContent, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();

        if (!$loaded) {
            return null;
        }

        $modified = false;
        foreach ($document->getElementsByTagName('a') as $anchor) {
            if (!$anchor->hasAttribute('href')) {
                continue;
            }
            $href = trim($anchor->getAttribute('href'));
            if ($href === '' || str_starts_with($href, '//') || strpos($href, ':') !== false || $href[0] !== '/') {
                continue;
            }

            $parts = parse_url($href);
            if (!empty($parts['path']) && preg_match('/[A-Z]/', $parts['path'])) {
                $lowercasePath = strtolower($parts['path']);
                if (isset($parts['query'])) {
                    $lowercasePath .= '?' . $parts['query'];
                }
                if (isset($parts['fragment'])) {
                    $lowercasePath .= '#' . $parts['fragment'];
                }
                if ($lowercasePath !== $href) {
                    $anchor->setAttribute('href', $lowercasePath);
                    $modified = true;
                }
            }
        }

        if (!$modified) {
            return null;
        }

        $html = $document->saveHTML();
        $html = preg_replace('/^<!DOCTYPE.*?>\s*/i', '', $html);
        $html = preg_replace('/^<\?xml.*?\?>\s*/i', '', $html);

        return trim($html);
    }
}
