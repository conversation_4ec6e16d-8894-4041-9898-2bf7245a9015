<?php


namespace Ersatzteilshop\Storefront\Controller;

use App\Product;
use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\Exception\CartTokenNotFoundException;
use Shopware\Core\Checkout\Cart\LineItem\LineItem;
use Shopware\Core\Checkout\Cart\LineItemFactoryRegistry;
use Shopware\Core\Checkout\Cart\Price\Struct\CalculatedPrice;
use Shopware\Core\Checkout\Cart\Tax\Struct\CalculatedTaxCollection;
use Shopware\Core\Checkout\Cart\Tax\Struct\TaxRuleCollection;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Shopware\Storefront\Page\Checkout\Cart\CheckoutCartPageLoader;
use Shopware\Storefront\Page\Checkout\Offcanvas\OffcanvasCartPageLoader;
use Symfony\Component\Routing\Annotation\Route;
use Shopware\Core\Checkout\Cart\SalesChannel\CartService;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Shopware\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoader;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Session;
use Ersatzteilshop\Service\CartService as ErsatzteilshopCartService;

/**
 * @RouteScope(scopes={"storefront"})
 */
class CheckoutController extends StorefrontController
{
    /**
     * @var CartService
     */
    private $cartService;
    /**
     * @var CheckoutConfirmPageLoader
     */
    private $confirmPageLoader;

    /**
     * @var CheckoutCartPageLoader
     */
    private $cartPageLoader;

    /**
     * @var Session
     */
    private $session;

    private LineItemFactoryRegistry $factory;
    private OffcanvasCartPageLoader $offcanvasCartPageLoader;

    private ErsatzteilshopCartService $ersatzteilshopCartService;

    public function __construct(
        CartService               $cartService,
        CheckoutConfirmPageLoader $confirmPageLoader,
        CheckoutCartPageLoader    $cartPageLoader,
        Session                   $session,
        LineItemFactoryRegistry   $factory,
        OffcanvasCartPageLoader $offcanvasCartPageLoader,
        EntityRepository $productRepository,
        ErsatzteilshopCartService $ersatzteilshopCartService
    ) {
        $this->cartService = $cartService;
        $this->confirmPageLoader = $confirmPageLoader;
        $this->session = $session;
        $this->factory = $factory;
        $this->cartPageLoader = $cartPageLoader;
        $this->offcanvasCartPageLoader = $offcanvasCartPageLoader;
        $this->productRepository = $productRepository;
        $this->ersatzteilshopCartService = $ersatzteilshopCartService;
    }

    /**
     * @Route("/checkout/confirm", name="frontend.checkout.confirm.page", options={"seo"="false"}, methods={"GET"}, defaults={"XmlHttpRequest"=true})
     */
    public function confirmPage(Request $request, SalesChannelContext $context): Response
    {
        if (!$context->getCustomer() && !$request->query->get('register') && !$request->query->get('guest')) {
            return $this->redirectToRoute('frontend.checkout.customer.selection');
        }


        if (!$context->getCustomer()) {
            return $this->redirectToRoute('frontend.checkout.register.page');
        }

        $page = $this->confirmPageLoader->load($request, $context);
        $this->addCartErrors($page->getCart());

        if ($this->cartService->getCart($context->getToken(), $context)->getLineItems()->count() === 0) {
            if ($orderId = $this->session->get('orderId')) {
                return $this->redirectToRoute('frontend.account.edit-order.page', ['orderId' => $orderId]);
            }
            return $this->redirectToRoute('frontend.checkout.cart.page');
        }

        return $this->renderStorefront(
            '@Storefront/storefront/page/checkout/confirm/index.html.twig',
            [
                'page' => $page,
                'tipProductId' => $this->ersatzteilshopCartService->getTipProductId(),
                'customer' => $context->getCustomer()
            ]
        );
    }

    /**
     * @Route("/checkout/customer-selection", name="frontend.checkout.customer.selection", options={"seo"="false"}, methods={"GET"})
     */
    public function customerSelection(): Response
    {
        return $this->renderStorefront('@Storefront/storefront/page/checkout/customer-selection.html.twig');
    }

    /**
     * @Route("/checkout/cart", name="frontend.checkout.cart.page", options={"seo"="false"}, methods={"GET"})
     */
    public function cartPage(Request $request, SalesChannelContext $context): Response
    {
        $page = $this->cartPageLoader->load($request, $context);

        $this->addCartErrors($page->getCart());

        $page->addExtensions([
            'prioProduct' => $this->getPrioProduct($context->getContext())
        ]);

        return $this->renderStorefront('@Storefront/storefront/page/checkout/cart/index.html.twig', ['page' => $page]);
    }

    public function getPrioProduct(Context $context): ?ProductEntity
    {
        $prioProductId = $this->ersatzteilshopCartService->getPrioProductId();
        if (!$prioProductId) {
            return null;
        }

        $criteria = new Criteria([$prioProductId]);
        $criteria->addAssociation('cover.media');

        return $this->productRepository->search($criteria, $context)->first();
    }

    /**
     * @Route("/checkout/offcanvas", name="frontend.cart.offcanvas", options={"seo"="false"}, methods={"GET"}, defaults={"XmlHttpRequest"=true})
     *
     * @throws CartTokenNotFoundException
     */
    public function offcanvas(Request $request, SalesChannelContext $context): Response
    {
        $page = $this->offcanvasCartPageLoader->load($request, $context);
        $cart = $page->getCart();
        $this->addCartErrors($cart);
        $cart->getErrors()->clear();

        $page->addExtensions([
            'prioProduct' => $this->getPrioProduct($context->getContext())
        ]);

        return $this->renderStorefront('@Storefront/storefront/component/checkout/offcanvas-cart.html.twig', ['page' => $page]);
    }

    /**
     * @Route("/checkout/tip", name="frontend.checkout.tip", options={"seo"="false"}, methods={"POST"}, defaults={"XmlHttpRequest"=true})
     */
    public function tip(Request $request, SalesChannelContext $context): Response
    {
        $amount = (float)$request->get('amount', 0);

        if ($amount < 0) {
            $amount = 0;
        }

        $cart = $this->cartService->getCart($context->getToken(), $context);
        $lineItems = $cart->getLineItems();

        foreach ($lineItems as $lineItem) {
            if ($lineItem->getReferencedId() === $this->ersatzteilshopCartService->getTipProductId()) {
                $this->cartService->remove($cart, $lineItem->getId(), $context);
            }
        }

        if ($amount > 0) {
            $lineItem = $this->factory->create([
                'type' => LineItem::PRODUCT_LINE_ITEM_TYPE,
                'referencedId' => $this->ersatzteilshopCartService->getTipProductId(),
                'quantity' => 1,
                'payload' => ['amount' => $amount],
            ], $context);
            $lineItem->setStackable(false);
            $lineItem->setGood(false);

            $this->cartService->add($cart, $lineItem, $context);
        }
        return $this->createActionResponse($request);
    }

    /**
     * @Route("/checkout/prio", name="frontend.checkout.prio", options={"seo"="false"}, methods={"POST"}, defaults={"XmlHttpRequest"=true})
     */
    public function prio(Request $request, SalesChannelContext $context): Response
    {
        $active = $request->get('active', 'off');

        $cart = $this->cartService->getCart($context->getToken(), $context);
        $lineItems = $cart->getLineItems();

        // remove existing prio if any
        foreach ($lineItems as $lineItem) {
            if ($lineItem->getReferencedId() === $this->ersatzteilshopCartService->getPrioProductId()) {
                $this->cartService->remove($cart, $lineItem->getId(), $context);
            }
        }

        if ($active == 'on') {
            $lineItem = $this->factory->create([
                'type' => LineItem::PRODUCT_LINE_ITEM_TYPE,
                'referencedId' => $this->ersatzteilshopCartService->getPrioProductId(),
                'quantity' => 1
            ], $context);
            $lineItem->setStackable(false);
            $lineItem->setGood(false);

            $this->cartService->add($cart, $lineItem, $context);
        }

        return $this->createActionResponse($request);
    }
}
