<?php declare(strict_types=1);

namespace Ersatzteilshop\Storefront\Controller;

use Ersatzteilshop\Core\Content\VideoLibrary\Aggregate\VideoLibraryComment\VideoLibraryCommentDefinition;
use Ersatzteilshop\Core\Event\VideoLibraryCommentCreatedEvent;
use Ersatzteilshop\Storefront\Page\VideoLibrary\VideoLibraryPageLoader;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\RangeFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Shopware\Core\Framework\Validation\DataValidationDefinition;
use Shopware\Core\Framework\Validation\DataValidator;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Shopware\Storefront\Framework\Captcha\Annotation\Captcha;
use Shopware\Storefront\Page\GenericPageLoaderInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * @RouteScope(scopes={"storefront"})
 */
class VideosController extends StorefrontController
{
    private VideoLibraryPageLoader $pageLoader;
    private EntityRepository $videoLibraryRepository;
    private EntityRepository $commentRepository;
    private EventDispatcherInterface $eventDispatcher;
    private DataValidator $validator;
    private GenericPageLoaderInterface $genericPageLoader;

    public function __construct(
        VideoLibraryPageLoader $pageLoader,
        EntityRepository $videoLibraryRepository,
        EntityRepository $commentRepository,
        EventDispatcherInterface $eventDispatcher,
        DataValidator $validator,
        GenericPageLoaderInterface $genericPageLoader
    ) {
        $this->pageLoader = $pageLoader;
        $this->videoLibraryRepository = $videoLibraryRepository;
        $this->commentRepository = $commentRepository;
        $this->eventDispatcher = $eventDispatcher;
        $this->validator = $validator;
        $this->genericPageLoader = $genericPageLoader;
    }

    /**
     * @Route(
     *     "/videos/uebersicht",
     *     name="frontend.video.library.overview",
     *     options={"seo"="false"},
     *     methods={"GET"}
     * )
     */
    public function overview(Request $request, SalesChannelContext $context): Response
    {
        return $this->renderStorefront(
            '@Storefront/storefront/page/video-library/overview.html.twig',
            ['page' => $this->pageLoader->loadOverview($request, $context)]
        );
    }


    /**
     * @Route(
     *     "/videos/{videoLibraryId}",
     *     name="frontend.video.library.page",
     *     options={"seo"="true"},
     *     methods={"GET"}
     * )
     */
    public function detail(Request $request, SalesChannelContext $context): Response
    {
        $page = $this->pageLoader->load($request, $context);
        $video = $page->getVideoLibrary();

        if ($video->getCmsPage() !== null) {
            return $this->renderStorefront(
                '@Ersatzteilshop/storefront/page/video-library/detail-cms.html.twig',
                [
                    'page' => $page,
                    'video' => $video
                ]
            );
        }

        return $this->renderStorefront(
            '@Ersatzteilshop/storefront/page/video-library/detail.html.twig',
            [
                'page' => $page,
                'video' => $video
            ]
        );
    }

    /**
     * @Route("/video-library/{videoLibraryId}/comment", name="frontend.video.library.comment.create", methods={"POST"}, defaults={"XmlHttpRequest"=true})
     * @Captcha
     */
    public function createComment(string $videoLibraryId, Request $request, RequestDataBag $data, SalesChannelContext $context): Response
    {
        $definition = new DataValidationDefinition('video_library_comment.create');
        $definition
            ->add('name', new NotBlank(), new Length(['max' => 255]))
            ->add('email', new NotBlank(), new Email(), new Length(['max' => 255]))
            ->add('content', new NotBlank());

        $violations = $this->validator->getViolations($data->all(), $definition);
        
        if ($violations->count() > 0) {
            if ($request->isXmlHttpRequest()) {
                $errors = [];
                foreach ($violations as $violation) {
                    $errors[$violation->getPropertyPath()] = $violation->getMessage();
                }
                
                return new JsonResponse([
                    'success' => false,
                    'message' => $this->trans('videolibrary.comment.error'),
                    'errors' => $errors
                ], 400);
            }
            
            $this->addFlash('danger', $this->trans('videolibrary.comment.error'));
            
            return $this->redirectToRoute('frontend.video.library.page', [
                'videoLibraryId' => $videoLibraryId,
                '_fragment' => 'video-comments'
            ]);
        }

        $criteria = new Criteria([$videoLibraryId]);
        $video = $this->videoLibraryRepository->search($criteria, $context->getContext())->first();
        
        if (!$video) {
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Video not found'
                ], 404);
            }

            throw $this->createNotFoundException('Video not found');
        }

        $recentCommentCheck = $this->checkRecentComment($videoLibraryId, $data->get('email'), $request, $context);
        if ($recentCommentCheck !== null) {
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => $this->trans('videolibrary.comment.tooRecent')
                ], 429);
            }

            $this->addFlash('danger', $this->trans('videolibrary.comment.tooRecent'));

            return $this->redirectToRoute('frontend.video.library.page', [
                'videoLibraryId' => $videoLibraryId,
                '_fragment' => 'video-comments'
            ]);
        }

        $commentId = Uuid::randomHex();
        $commentData = [
            'id' => $commentId,
            'videoLibraryId' => $videoLibraryId,
            'languageId' => $context->getLanguageId(),
            'customerId' => $context->getCustomer() ? $context->getCustomer()->getId() : null,
            'name' => strip_tags((string) $data->get('name')),
            'email' => $data->get('email'),
            'subject' => $data->get('subject') ? strip_tags((string) $data->get('subject')) : null,
            'content' => strip_tags((string) $data->get('content'), '<p><br><strong><em><u><ul><ol><li>'),
            'status' => VideoLibraryCommentDefinition::STATUS_PENDING,
        ];

        $this->commentRepository->create([$commentData], $context->getContext());

        $event = new VideoLibraryCommentCreatedEvent($videoLibraryId, $commentData, $context);
        $this->eventDispatcher->dispatch($event, VideoLibraryCommentCreatedEvent::EVENT_NAME);

        if ($request->isXmlHttpRequest()) {
            return new JsonResponse([
                'success' => true,
                'message' => $this->trans('videolibrary.comment.success'),
                'commentId' => $commentId
            ]);
        }

        $this->addFlash('success', $this->trans('videolibrary.comment.success'));
        
        return $this->redirectToRoute('frontend.video.library.page', [
            'videoLibraryId' => $videoLibraryId,
            '_fragment' => 'video-comments'
        ]);
    }

    /**
     * @Route("/video-library/{videoLibraryId}/like", name="frontend.video.library.like", methods={"POST"}, defaults={"XmlHttpRequest"=true, "csrf_protected"=false})
     */
    public function like(string $videoLibraryId, Request $request, SalesChannelContext $context): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid request'], 400);
        }

        $criteria = new Criteria([$videoLibraryId]);
        $video = $this->videoLibraryRepository->search($criteria, $context->getContext())->first();

        if (!$video) {
            return new JsonResponse(['success' => false, 'message' => 'Video not found'], 404);
        }

        $customFields = $video->getCustomFields() ?? [];
        $currentLikes = $customFields['ersatzteilshop_video_library_likes'] ?? 0;
        $isLiked = $request->request->get('liked') === '1';

        if ($isLiked) {
            $newLikes = $currentLikes + 1;
        } else {
            $newLikes = max(0, $currentLikes - 1);
        }

        $customFields['ersatzteilshop_video_library_likes'] = $newLikes;

        $this->videoLibraryRepository->update([
            [
                'id' => $videoLibraryId,
                'customFields' => $customFields,
            ]
        ], $context->getContext());

        return new JsonResponse([
            'success' => true,
            'likes' => $newLikes,
            'liked' => $isLiked
        ]);
    }

    /**
     * @Route("/video-library/{videoLibraryId}/share", name="frontend.video.library.share", methods={"POST"}, defaults={"XmlHttpRequest"=true, "csrf_protected"=false})
     */
    public function share(string $videoLibraryId, Request $request, SalesChannelContext $context): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid request'], 400);
        }

        $criteria = new Criteria([$videoLibraryId]);
        $video = $this->videoLibraryRepository->search($criteria, $context->getContext())->first();

        if (!$video) {
            return new JsonResponse(['success' => false, 'message' => 'Video not found'], 404);
        }

        $customFields = $video->getCustomFields() ?? [];
        $currentShares = $customFields['ersatzteilshop_video_library_shares'] ?? 0;
        $newShares = $currentShares + 1;

        $customFields['ersatzteilshop_video_library_shares'] = $newShares;

        $this->videoLibraryRepository->update([
            [
                'id' => $videoLibraryId,
                'customFields' => $customFields,
            ]
        ], $context->getContext());

        return new JsonResponse([
            'success' => true,
            'shares' => $newShares
        ]);
    }

    /**
     * @Route("/video-library/{videoLibraryId}/request", name="frontend.video.library.request", methods={"POST"}, defaults={"XmlHttpRequest"=true, "csrf_protected"=false})
     */
    public function requestVideo(string $videoLibraryId, Request $request, SalesChannelContext $context): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid request'], 400);
        }

        $criteria = new Criteria([$videoLibraryId]);
        $video = $this->videoLibraryRepository->search($criteria, $context->getContext())->first();

        if (!$video) {
            return new JsonResponse(['success' => false, 'message' => 'Video not found'], 404);
        }

        $customFields = $video->getCustomFields() ?? [];
        $currentRequests = $customFields['ersatzteilshop_video_library_video_requests'] ?? 0;
        $newRequests = $currentRequests + 1;

        $customFields['ersatzteilshop_video_library_video_requests'] = $newRequests;

        $this->videoLibraryRepository->update([
            [
                'id' => $videoLibraryId,
                'customFields' => $customFields,
            ]
        ], $context->getContext());

        return new JsonResponse([
            'success' => true,
            'requests' => $newRequests
        ]);
    }

    private function checkRecentComment(string $videoLibraryId, string $email, Request $request, SalesChannelContext $context): ?string
    {
        $thirtyMinutesAgo = new \DateTime('-30 minutes');

        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('videoLibraryId', $videoLibraryId));
        $criteria->addFilter(new EqualsFilter('email', $email));
        $criteria->addFilter(new RangeFilter('createdAt', [
            RangeFilter::GTE => $thirtyMinutesAgo->format('Y-m-d H:i:s')
        ]));
        $criteria->addSorting(new FieldSorting('createdAt', FieldSorting::DESCENDING));
        $criteria->setLimit(1);

        $recentComment = $this->commentRepository->search($criteria, $context->getContext())->first();

        return $recentComment ? $recentComment->getId() : null;
    }
}
