<?php declare(strict_types=1);

namespace Ersatzteilshop\Core\Content\Appliance\SalesChannel;

use Ersatzteilshop\Core\Content\Appliance\Aggregate\ApplianceProductSeoDefinition;
use Ersatzteilshop\Core\Content\Appliance\Aggregate\ApplianceProductSeoEntity;
use NewsletterSendinblue\Model\Field;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\OrFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Symfony\Component\HttpFoundation\Request;
use Shopware\Core\Framework\Struct\ArrayStruct;
use Ersatzteilshop\Service\ProductReviewLoader;
use XantenAswo\Event\LoadAswoApplianceProductEvent;
use Shopware\Core\Content\Product\ProductDefinition;
use XantenAswo\Event\LoadTotalAswoApplianceProductEvent;
use Ersatzteilshop\Core\Content\Appliance\ApplianceEntity;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Ersatzteilshop\Core\Content\Appliance\ApplianceCollection;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Content\Product\Events\ProductListingResultEvent;
use Shopware\Core\Content\Product\Events\ProductListingCriteriaEvent;
use Ersatzteilshop\Core\Content\Product\Filter\NotShadowProductFilter;
use Shopware\Core\Content\Product\SalesChannel\ProductAvailableFilter;
use Ersatzteilshop\Core\Content\Product\Filter\OnlyShadowProductFilter;
use Shopware\Core\Framework\Plugin\Exception\DecorationPatternException;
use Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceProduct;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepositoryInterface;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelEntityLoadedEvent;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingResult;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepositoryInterface;
use Shopware\Core\Content\Product\Aggregate\ProductVisibility\ProductVisibilityDefinition;
use Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerCollection;
use function Ramsey\Uuid\v1;

class ApplianceProductRoute extends AbstractApplianceProductRoute
{
    public function __construct(
        private SalesChannelRepositoryInterface         $productRepository,
        private EntityRepositoryInterface               $applianceProductSeoRepository,
        private ProductDefinition                       $productDefinition,
        private ProductReviewLoader                     $productReviewLoader,
        private ApplianceProduct\LoadPaginatedAppliance $loadPaginatedAppliance,
        private SystemConfigService                     $systemConfigService,
        private EventDispatcherInterface                $dispatcher
    ) {
    }

    public function getDecorated(): AbstractApplianceProductRoute
    {
        throw new DecorationPatternException(self::class);
    }

    public function loadProducts(
        ApplianceEntity     $appliance,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): ProductListingResult
    {
        $criteria = new Criteria();
        #$criteria->addFilter(new NotShadowProductFilter());
        $products = $this->doLoadProducts(
            $appliance->getId(),
            $request,
            $salesChannelContext,
            $criteria
        );
        #$this->loadShadowProducts($appliance, $products, $request, $salesChannelContext);
        $this->loadAswoProducts($appliance, $products, $request, $salesChannelContext);
        $this->loadProductRatings($products, $salesChannelContext);
        return $products;
    }

    public function countProducts(
        ApplianceEntity     $appliance,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): int
    {
        $result = $this->doLoadProducts(
            $appliance->getId(),
            $request,
            $salesChannelContext
        );
        $this->loadAswoProductCount($appliance, $result, $request, $salesChannelContext);

        $totalAswoProduct = 0;
        if ($result->hasExtension(self::TOTAL_ASWO_PRODUCT)) {
            $totalAswoProduct = $result->getExtension(self::TOTAL_ASWO_PRODUCT)->get('value');
        }

        return $result->getTotal() + $totalAswoProduct;
    }

    private function loadAswoProducts(
        ApplianceEntity         $appliance,
        ProductListingResult    $products,
        Request                 $request,
        SalesChannelContext     $salesChannelContext
    ): void {
        if ($products->count() === $products->getLimit()) {
            return;
        }

        if ($appliance->getModelCodeLength() < $this->getMinModelCodeLength()) {
            return;
        }

        $page = (int)$request->get('p', 1);

        $pageNumber = (int)($page - floor($products->getTotal() / $products->getLimit()));

        $event = new LoadAswoApplianceProductEvent(
            $appliance->getModelCode(),
            $pageNumber,
            $request->get('search'),
            $salesChannelContext->getContext()
        );

        $this->dispatcher->dispatch($event);

        if ($event->getProducts()->count()) {
            $this->dispatcher->dispatch(
                new SalesChannelEntityLoadedEvent(
                    $this->productDefinition,
                    $event->getProducts()->getElements(),
                    $salesChannelContext
                ),
                'sales_channel.product.loaded'
            );
        }

        $products->merge($event->getProducts());

        $products->addExtension(
            AbstractApplianceProductRoute::TOTAL_ASWO_PRODUCT,
            new ArrayStruct(['value' => $event->getTotal()])
        );
    }

    private function loadShadowProducts(
        ApplianceEntity         $appliance,
        ProductListingResult    &$products,
        Request                 $request,
        SalesChannelContext     $salesChannelContext
    ): void {
        // Only load shadow products if there is none products left
        if ($products->count()) {
            return;
        }

        $page = (int)$request->get('p', 1);

        $totalPage = floor($products->getTotal() / $products->getLimit());
        if ($products->hasExtension(self::TOTAL_ASWO_PRODUCT)) {
            $totalAswoProduct = $products->getExtension(self::TOTAL_ASWO_PRODUCT)->get('value');
            $totalPage += floor($totalAswoProduct / self::ASWO_PRODUCT_PER_PAGE);
        }

        $request->query->set(
            'p',
            (int)($page - $totalPage)
        );

        $criteria = new Criteria();
        $criteria->addFilter(new OnlyShadowProductFilter());

        $products = $this->doLoadProducts(
            $appliance->getId(),
            $request,
            $salesChannelContext,
            $criteria
        );
    }

    private function loadAswoProductCount(
        ApplianceEntity      $appliance,
        ProductListingResult $result,
        Request              $request,
        SalesChannelContext  $salesChannelContext
    ): void
    {
        if ($appliance->getModelCodeLength() < $this->getMinModelCodeLength()) {
            return;
        }

        $this->dispatcher->dispatch($event = new LoadTotalAswoApplianceProductEvent(
            $appliance->getModelCode(),
            $request->get('search'),
            $salesChannelContext->getContext()
        ));

        $result->addExtension(
            AbstractApplianceProductRoute::TOTAL_ASWO_PRODUCT,
            new ArrayStruct(['value' => $event->getTotal()])
        );
    }

    private function doLoadProducts(
        string              $applianceId,
        Request             $request,
        SalesChannelContext $salesChannelContext,
        Criteria            $criteria = null
    ): ProductListingResult
    {
        $salesChannelContext->addState(Context::STATE_ELASTICSEARCH_AWARE);
        $request->query->set('category-filter', true);

        $criteria = $criteria ?? new Criteria();
        $criteria->setTotalCountMode(Criteria::TOTAL_COUNT_MODE_EXACT);
        if ($request->get('search')) {
            $criteria->setTerm($request->get('search'));
        }
        $criteria->addSorting(new FieldSorting('customFields.product_featured', FieldSorting::DESCENDING));

        $criteria->addFilter(new EqualsFilter('appliances.id', $applianceId));
        $criteria->addFilter(
            new ProductAvailableFilter(
                $salesChannelContext->getSalesChannel()->getId(),
                ProductVisibilityDefinition::VISIBILITY_ALL
            )
        );

        $this->dispatcher->dispatch(
            new ProductListingCriteriaEvent($request, $criteria, $salesChannelContext)
        );

        $entities = $this->productRepository->search($criteria, $salesChannelContext);

        /** @var ProductListingResult $result */
        $result = ProductListingResult::createFrom($entities);
        $result->addState(...$entities->getStates());

        $this->dispatcher->dispatch(
            new ProductListingResultEvent($request, $result, $salesChannelContext)
        );
        return $result;
    }

    private function loadProductRatings(ProductListingResult $products, SalesChannelContext $salesChannelContext): void
    {
        $this->productReviewLoader->load(
            $products->getEntities(),
            $salesChannelContext->getContext()
        );
    }

    public function loadAllFromProduct(
        string              $productId,
        SalesChannelContext $salesChannelContext
    ): array {
        $criteria = new Criteria();
        $criteria->addFilter(new EqualsFilter('productId', $productId));
        $criteria->setIncludes([
            'appliance_product_seo' => ['applianceId'],
            'appliance' => ['id', 'modelName', 'modelCode', 'seoUrls', 'whitelistedSitemap']
        ]);
        $criteria->addAssociations(['appliance.manufacturer', 'appliance.seoUrls']);
        $criteria->addSorting(
            new FieldSorting('appliance.manufacturer.name', FieldSorting::ASCENDING),
            new FieldSorting('appliance.modelName', FieldSorting::ASCENDING),
            new FieldSorting('appliance.modelCode', FieldSorting::ASCENDING)
        );
        /** @var ApplianceProductSeoEntity[] $relations */
        $relations = $this->applianceProductSeoRepository->search($criteria, $salesChannelContext->getContext())->getElements();

        $appliances = [];
        $manufacturers = [];

        $storefrontUrl = $salesChannelContext->getSalesChannel()->getDomains()->get($salesChannelContext->getDomainId())->getUrl();

        foreach ($relations as $relation) {
            $appliance = $relation->getAppliance();
            $manufacturers[$appliance->getManufacturerId()] = [
                'name' => $appliance->getManufacturer()->getName(),
                'mediaUrl' => $appliance->getManufacturer()->getMedia()?->getUrl() ?? null
            ];
            $appliances[] = [
                $appliance->getManufacturer()->getName() ?? null,
                $appliance->getModelName() ?? null,
                $appliance->getModelCode() ?? null,
                sprintf(
                    '%s/%s',
                    rtrim($storefrontUrl, '/'),
                    ltrim($appliance->getSeoUrl($salesChannelContext) ?? ('appliance/' . $appliance->getId()), '/')
                ),
                $appliance->getWhitelistedSitemap() ?? false,
            ];
        }

        $ids = $this->getConfigHiddenManufacturers();
        foreach ($ids as $id) {
            unset($manufacturers[$id]);
        }

        return ['appliances' => $appliances, 'manufacturers' => array_values($manufacturers)];
    }

    public function loadFromProductWithPagination(
        string              $productId,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): ApplianceCollection {
        return $this->loadPaginatedAppliance->load($productId, $request, $salesChannelContext);
    }

    public function loadManufacturersFromProduct(
        string              $productId,
        SalesChannelContext $salesChannelContext
    ): ProductManufacturerCollection {
        return $this->loadPaginatedAppliance->loadManufacturers($productId, $salesChannelContext);
    }

    private function getMinModelCodeLength(): int
    {
        return $this->systemConfigService->get('Ersatzteilshop.config.minModelCodeLength') ?? 5;
    }

    private function getConfigHiddenManufacturers(): array
    {
        return $this->systemConfigService->get('Ersatzteilshop.config.manufacturersHidden') ?? [];
    }

    public function loadProductsByCategory(
        ApplianceEntity     $appliance,
        string              $categoryId,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): array
    {
        $criteria = new Criteria();
        $criteria->addFilter(new ContainsFilter('categoryTree', $categoryId));
        $criteria->addAssociations(['seoUrls']);
        $products = $this->doLoadProducts(
            $appliance->getId(),
            $request,
            $salesChannelContext,
            $criteria
        );
        //$this->loadAswoProducts($appliance, $products, $request, $salesChannelContext);
        $this->loadProductRatings($products, $salesChannelContext);
        return [
            'products' => $products,
            'total' => $products->getTotal()
        ];
    }

    public function loadSearchFilteredProducts(
        ApplianceEntity     $appliance,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): ProductListingResult
    {
        $salesChannelContext->addState(Context::STATE_ELASTICSEARCH_AWARE);

        $criteria = new Criteria();
        $criteria->setTotalCountMode(Criteria::TOTAL_COUNT_MODE_EXACT);

        $term = trim((string) $request->query->get('search', ''));
        if ($term !== '') {
            if (mb_strlen($term) > 20) {
                $term = mb_substr($term, 0, 20);
            }

            $term = preg_replace('/[^\p{L}\p{N}\s\-\.]/u', '', $term);
            $term = str_replace(['\\', '%', '_'], ['\\\\', '\%', '\_'], $term);
            $criteria->addFilter(new OrFilter([
                new ContainsFilter('name', $term),
                new ContainsFilter('description', $term),
            ]));
        }

        $criteria->addSorting(new FieldSorting('customFields.product_featured', FieldSorting::DESCENDING));
        $criteria->addFilter(new EqualsFilter('appliances.id', $appliance->getId()));
        $criteria->addFilter(
            new ProductAvailableFilter(
                $salesChannelContext->getSalesChannel()->getId(),
                ProductVisibilityDefinition::VISIBILITY_ALL
            )
        );

        $this->dispatcher->dispatch(
            new ProductListingCriteriaEvent($request, $criteria, $salesChannelContext)
        );

        $criteria->setLimit(50);

        $entities = $this->productRepository->search($criteria, $salesChannelContext);
        /** @var ProductListingResult $result */
        $result = ProductListingResult::createFrom($entities);
        $result->addState(...$entities->getStates());

        $this->dispatcher->dispatch(
            new ProductListingResultEvent($request, $result, $salesChannelContext)
        );

        $this->loadAswoProducts($appliance, $result, $request, $salesChannelContext);
        $this->loadProductRatings($result, $salesChannelContext);

        return $result;
    }
}
