<?php declare(strict_types=1);

namespace Ersatzteilshop\Service;

use Ersatzteilshop\Core\Content\VideoLibrary\VideoLibraryEntity;
use Shopware\Core\Content\Cms\CmsPageEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepositoryInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\Struct\ArrayStruct;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class VideoLibrarySchemaService
{
    private RequestStack $requestStack;
    private UrlGeneratorInterface $urlGenerator;
    private EntityRepositoryInterface $videoLibraryRepository;

    public function __construct(
        RequestStack $requestStack,
        UrlGeneratorInterface $urlGenerator,
        EntityRepositoryInterface $videoLibraryRepository
    ) {
        $this->requestStack = $requestStack;
        $this->urlGenerator = $urlGenerator;
        $this->videoLibraryRepository = $videoLibraryRepository;
    }

    public function generateSchemas(VideoLibraryEntity $video, ?Context $context = null): ArrayStruct
    {
        $schemas = [];
        $displayStructuredData = $video->getDisplayStructuredData() ?? true;

        if ($displayStructuredData) {
            $articleSchema = $this->generateArticleSchema($video);
            if ($articleSchema) {
                $schemas[] = $articleSchema;
            }
        }

        $schemas[] = $this->generateBreadcrumbSchema($video);

        if ($video->getUrl() && !empty($video->getUrl()) && !$video->getCmsPage()) {
            $schemas[] = $this->generateVideoObjectSchema($video);
        }
        
        if ($video->getCmsPageId() && $video->getCmsPage() && $video->getCmsPage()->getType() === 'video_landing') {
            $contextToUse = $context ?? Context::createDefaultContext();
            $cmsVideoSchemas = $this->generateCmsPageVideoSchemas($video->getCmsPage(), $contextToUse, $video->getManufacturers());
            $schemas = array_merge($schemas, $cmsVideoSchemas);
        }

        if ($displayStructuredData && !$video->getCmsPageId()) {
            $howtoSchema = $this->generateHowtoSchema($video);
            if ($howtoSchema) {
                $schemas[] = $howtoSchema;
            }
        }

        if ($displayStructuredData && !$video->getCmsPageId()) {
            $faqSchema = $this->generateFaqSchema($video);
            if ($faqSchema) {
                $schemas[] = $faqSchema;
            }
        }

        return new ArrayStruct([
            '@context' => 'https://schema.org',
            '@graph' => $schemas
        ]);
    }

    private function generateArticleSchema(VideoLibraryEntity $video): ?array
    {
        $defaultTranslation = $this->getDefaultTranslation($video);
        $articleMetadata = $defaultTranslation?->getArticleMetadata();

        if (!$articleMetadata || !is_array($articleMetadata)) {
            return null;
        }
        $currentUrl = $this->getCurrentUrl();
        $articleSchema = [
            '@type' => 'Article',
            'headline' => $video->getTitle(),
            'description' => strip_tags($articleMetadata['description'] ?? ''),
            'author' => [
                '@type' => 'Person',
                'name' => $articleMetadata['author'] ?? 'Ersatzteilshop Team'
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'K11 Ersatzteilshop GmbH',
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => 'https://static.ersatzteilshop.de/public/media/03/b7/f3/1681910212/Favicon_Ersatzteilshop.svg'
                ]
            ],
            'datePublished' => $video->getCreatedAt()?->format('c') ?? date('c'),
            'dateModified' => $video->getUpdatedAt()?->format('c') ?? date('c'),
            'mainEntityOfPage' => $currentUrl
        ];

        if (!empty($articleMetadata['keywords']) && is_array($articleMetadata['keywords'])) {
            $articleSchema['keywords'] = implode(', ', $articleMetadata['keywords']);
        }

        return $articleSchema;
    }

    private function generateBreadcrumbSchema(VideoLibraryEntity $video): array
    {
        $currentUrl = $this->getCurrentUrl();
        
        return [
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'item' => [
                        '@id' => $this->urlGenerator->generate('frontend.home.page', [], UrlGeneratorInterface::ABSOLUTE_URL),
                        'name' => 'Ersatzteilshop'
                    ]
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'item' => [
                        '@id' => $this->urlGenerator->generate('frontend.video.library.overview', [], UrlGeneratorInterface::ABSOLUTE_URL),
                        'name' => 'Ratgeber'
                    ]
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 3,
                    'item' => [
                        '@id' => $currentUrl,
                        'name' => $video->getTitle()
                    ]
                ]
            ]
        ];
    }

    private function generateVideoObjectSchema(VideoLibraryEntity $video): array
    {
        $defaultTranslation = $this->getDefaultTranslation($video);
        $videoObjectMetadata = $defaultTranslation?->getVideoObjectMetadata();

        $videoDescription = $video->getDescription() ?? $video->getSeoShortDescription() ?? '';
        $videoTitle = $video->getTitle();
        $videoThumbnailUrl = "https://img.youtube.com/vi/" . $video->getYoutubeId() . "/0.jpg";
        $videoUploadDate = $video->getCreatedAt() ? $video->getCreatedAt()->format('c') : date('c');
        $videoContentUrl = "https://youtu.be/" . $video->getYoutubeId();
        $videoEmbedUrl = "https://www.youtube.com/embed/" . $video->getYoutubeId();

        if ($videoObjectMetadata) {
            if (!empty($videoObjectMetadata['title'])) {
                $videoTitle = $videoObjectMetadata['title'];
            }
            if (!empty($videoObjectMetadata['aiDescription'])) {
                $videoDescription = $videoObjectMetadata['aiDescription'];
            } elseif (!empty($videoObjectMetadata['description'])) {
                $videoDescription = $videoObjectMetadata['description'];
            }
            if (!empty($videoObjectMetadata['thumbnailUrl'])) {
                $videoThumbnailUrl = $videoObjectMetadata['thumbnailUrl'];
            }
            if (!empty($videoObjectMetadata['uploadDate'])) {
                $videoUploadDate = $videoObjectMetadata['uploadDate'];
            }
            if (!empty($videoObjectMetadata['contentUrl'])) {
                $videoContentUrl = $videoObjectMetadata['contentUrl'];
            }
            if (!empty($videoObjectMetadata['embedUrl'])) {
                $videoEmbedUrl = $videoObjectMetadata['embedUrl'];
            }
        }

        $videoSchema = [
            '@type' => 'VideoObject',
            'name' => $videoTitle,
            'description' => strip_tags($videoDescription),
            'thumbnailUrl' => $videoThumbnailUrl,
            'uploadDate' => $videoUploadDate,
            'contentUrl' => $videoContentUrl,
            'embedUrl' => $videoEmbedUrl
        ];

        if ($videoObjectMetadata && !empty($videoObjectMetadata['duration'])) {
            $videoSchema['duration'] = $videoObjectMetadata['duration'];
        }

        if ($videoObjectMetadata && !empty($videoObjectMetadata['viewCount'])) {
            $videoSchema['interactionStatistic'] = [
                '@type' => 'InteractionCounter',
                'interactionType' => ['@type' => 'WatchAction'],
                'userInteractionCount' => $videoObjectMetadata['viewCount']
            ];
        }

        return $videoSchema;
    }

    private function generateFaqSchema(VideoLibraryEntity $video): ?array
    {
        $defaultTranslation = $this->getDefaultTranslation($video);
        $faqMetadata = $defaultTranslation?->getFaqMetadata();
        if (!$faqMetadata || !is_array($faqMetadata) || empty($faqMetadata)) {
            return null;
        }

        $faqItems = [];
        foreach ($faqMetadata as $item) {
            if (!is_array($item) || empty($item['question']) || empty($item['answer'])) {
                continue;
            }

            $faqItems[] = [
                '@type' => 'Question',
                'name' => $item['question'],
                'acceptedAnswer' => [
                    '@type' => 'Answer',
                    'text' => strip_tags($item['answer'])
                ]
            ];
        }

        if (empty($faqItems)) {
            return null;
        }

        return [
            '@type' => 'FAQPage',
            'name' => 'FAQ zu: ' . $video->getTitle(),
            'mainEntity' => $faqItems
        ];
    }

    private function generateHowtoSchema(VideoLibraryEntity $video): ?array
    {
        $defaultTranslation = $this->getDefaultTranslation($video);
        $howtoMetadata = $defaultTranslation?->getHowtoMetadata();

        if (!$howtoMetadata || !is_array($howtoMetadata)) {
            return null;
        }
        
        if (isset($howtoMetadata['name']) || isset($howtoMetadata['step'])) {
            $schema = [
                '@type' => 'HowTo',
                'name' => $howtoMetadata['name'] ?? $video->getTitle(),
                'description' => $howtoMetadata['description'] ?? strip_tags($video->getDescription() ?? '')
            ];

            if (isset($howtoMetadata['step']) && is_array($howtoMetadata['step']) && !empty($howtoMetadata['step'])) {
                $steps = [];
                foreach ($howtoMetadata['step'] as $step) {
                    if (is_array($step) && !empty($step['name'])) {
                        $stepData = [
                            '@type' => 'HowToStep',
                            'name' => $step['name']
                        ];

                        if (!empty($step['text'])) {
                            $stepData['text'] = $step['text'];
                        }

                        $steps[] = $stepData;
                    }
                }

                if (!empty($steps)) {
                    $schema['step'] = $steps;
                }
            }

            if (isset($howtoMetadata['tool']) && is_array($howtoMetadata['tool']) && !empty($howtoMetadata['tool'])) {
                $tools = [];
                foreach ($howtoMetadata['tool'] as $tool) {
                    if (is_array($tool) && !empty($tool['name'])) {
                        $tools[] = [
                            '@type' => 'HowToTool',
                            'name' => $tool['name'],
                            'description' => $tool['description'] ?? ''
                        ];
                    }
                }

                if (!empty($tools)) {
                    $schema['tool'] = $tools;
                }
            }

            if (isset($howtoMetadata['supply']) && is_array($howtoMetadata['supply']) && !empty($howtoMetadata['supply'])) {
                $supplies = [];
                foreach ($howtoMetadata['supply'] as $supply) {
                    if (is_array($supply) && !empty($supply['name'])) {
                        $supplies[] = [
                            '@type' => 'HowToSupply',
                            'name' => $supply['name'],
                            'description' => $supply['description'] ?? ''
                        ];
                    }
                }

                if (!empty($supplies)) {
                    $schema['supply'] = $supplies;
                }
            }
            return $schema;
        }

        if (empty($howtoMetadata)) {
            return null;
        }

        $steps    = [];
        $position = 1;
        foreach ($howtoMetadata as $item) {
            if (!is_array($item) || empty($item['name']) || empty($item['text'])) {
                continue;
            }

            $steps[] = [
                '@type' => 'HowToStep',
                'position' => $position++,
                'name' => $item['name'],
                'text' => strip_tags($item['text'])
            ];
        }

        if (empty($steps)) {
            return null;
        }

        return [
            '@type' => 'HowTo',
            'name' => $video->getTitle(),
            'step' => $steps
        ];
    }

    private function getDefaultTranslation(VideoLibraryEntity $video)
    {
        $translations = $video->translations ?? null;
        return $translations && $translations->count() > 0 ? $translations->first() : null;
    }

    private function getCurrentUrl(): string
    {
        $request = $this->requestStack->getCurrentRequest();
        return $request ? $request->getUri() : '';
    }

    private function extractYoutubeUrl(string $url): ?string
    {
        $patterns = [
            '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/',
            '/^([a-zA-Z0-9_-]+)$/'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }

    private function findVideoByYoutubeUrl(string $url, Context $context, ?array $preferredManufacturers = null): ?VideoLibraryEntity
    {
        $criteria = new Criteria();
        $criteria->addFilter(new ContainsFilter('url', $url));
        $criteria->addAssociation('translations');
        
        $searchResult = $this->videoLibraryRepository->search($criteria, $context);
        
        if ($searchResult->count() <= 1) {
            return $searchResult->first();
        }
        
        if ($preferredManufacturers !== null && !empty($preferredManufacturers)) {
            foreach ($searchResult->getEntities() as $video) {
                $videoManufacturers = $video->getManufacturers();
                if ($videoManufacturers !== null) {
                    foreach ($videoManufacturers as $manufacturer) {
                        if (in_array($manufacturer, $preferredManufacturers, true)) {
                            return $video;
                        }
                    }
                }
            }
        }
        return $searchResult->first();
    }

    public function generateCmsPageVideoSchemas(CmsPageEntity $cmsPage, Context $context, ?array $preferredManufacturers = null): array
    {
        $videoSchemas = [];
        $processedVideoIds = [];
        if (!$cmsPage->getSections()) {
            return $videoSchemas;
        }
        
        foreach ($cmsPage->getSections() as $section) {
            if (!$section->getBlocks()) {
                continue;
            }
            
            foreach ($section->getBlocks() as $block) {
                if (!$block->getSlots()) {
                    continue;
                }
                
                foreach ($block->getSlots() as $slot) {
                    if ($slot->getType() === 'youtube-video') {
                        $config = $slot->getConfig();
                        $videoUrl = $config['videoID']['value'] ?? null;
                        if ($videoUrl) {
                            $videoUrl = $this->extractYoutubeUrl($videoUrl);
                            if ($videoUrl) {
                                $video = $this->findVideoByYoutubeUrl($videoUrl, $context, $preferredManufacturers);
                                if ($video && !isset($processedVideoIds[$video->getId()])) {
                                    $videoSchemas[] = $this->generateVideoObjectSchema($video);
                                    $processedVideoIds[$video->getId()] = true;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return $videoSchemas;
    }
}
