{% sw_extends '@Storefront/storefront/component/address/address-editor-modal-create-address.html.twig' %}

{% block component_address_address_editor_modal_create_address_title %}

    <div class="card-title create-address-title" style="font-size: 18px; font-weight: 700">
        {% block component_address_address_editor_modal_create_address_title_text %}
            {% if address %}
                {{ "account.addressEditWelcome"|trans|sw_sanitize }}
            {% else %}
                {{ "account.addressCreateWelcome"|trans|sw_sanitize }}
            {% endif %}
        {% endblock %}
    </div>
{% endblock %}

{% block component_address_address_editor_modal_create_address_form %}
<form method="post"
      action="{{ path('frontend.account.addressbook') }}"
      class="js-close-address-editor"
      id="js-close-address-editor-form"
      data-form-validation="true"
      data-form-ajax-submit="true"
      data-form-ajax-submit-options='{{ formAjaxSubmitOptions|json_encode }}'>
    {{ block('component_address_address_editor_modal_create_address_form_csrf') }}
    {{ block('component_address_address_editor_modal_create_address_form_hidden_inputs') }}
    {{ block('component_address_address_editor_modal_create_address_form_fields_include') }}
    {{ block('component_address_address_editor_modal_create_address_required_hint') }}
    {{ block('component_address_address_editor_modal_create_address_form_actions') }}
</form>
{% endblock %}

{% block component_address_address_editor_modal_create_address_form_fields_include %}
    {% set formAddressPrefix = address ? 'edit' : 'create' %}

    {% if changeShipping %}
        <div class="register-shipping-address-option" data-shipping-address-option="true">
            <div class="form-check">
                <input class="form-check-input js-shipping-address-option"
                       type="radio"
                       {% if address.customFields.addressType != '' and address %}  disabled {% endif %}
                       name="shippingAddressOption"
                       id="{{ formAddressPrefix }}differentShippingAddressOption"
                       value=""
                        {% if address.get('customFields').addressType|trim == '' %}
                            checked
                        {% endif %}
                       data-shipping-address-option-target=".{{ formAddressPrefix }}js-form-different-shipping-address"
                >
                <label class="form-check-label" for="{{ formAddressPrefix }}differentShippingAddressOption">
                    {{ "account.changeNormalShippingAddress"|trans|sw_sanitize }}
                </label>
            </div>

            <div class="form-check">
                <input class="form-check-input js-shipping-address-option"
                       type="radio"
                       {% if address.customFields.addressType != 'PackstationAddress' and address %}  disabled {% endif %}
                       name="shippingAddressOption"
                       id="{{ formAddressPrefix }}packstationShippingAddressOption"
                       value="PackstationAddress"
                        {% if address.get('customFields').addressType == 'PackstationAddress' %}
                            checked
                        {% endif %}
                       data-shipping-address-option-target=".{{ formAddressPrefix }}js-form-packstation-shipping-address"
                >
                <label class="form-check-label" for="{{ formAddressPrefix }}packstationShippingAddressOption">
                    {{ "account.registerPackstationShippingAddress"|trans|sw_sanitize }}
                </label>
            </div>

            <div class="form-check">
                <input class="form-check-input js-shipping-address-option"
                       type="radio"
                       {% if address.customFields.addressType != 'PostfilialeAddress' and address %}  disabled {% endif %}
                       name="shippingAddressOption"
                       id="{{ formAddressPrefix }}postfilialeShippingAddressOption"
                       value="PostfilialeAddress"
                       {% if address.get('customFields').addressType == 'PostfilialeAddress' %}
                          checked
                       {% endif %}
                       data-shipping-address-option-target=".{{ formAddressPrefix }}js-form-postfiliale-shipping-address"
                >
                <label class="form-check-label" for="{{ formAddressPrefix }}postfilialeShippingAddressOption">
                    {{ "account.registerPostfilialeShippingAddress"|trans|sw_sanitize }}
                </label>
            </div>

            <div class="form-check">
                <input class="form-check-input js-shipping-address-option"
                       type="radio"
                       {% if address.customFields.addressType != 'MeineinkaufAddress' and address %}  disabled {% endif %}
                       name="shippingAddressOption"
                       id="{{ formAddressPrefix }}meineinkaufShippingAddressOption"
                       value="meineinkaufShippingAddress"
                        {% if address.get('customFields').addressType == 'MeineinkaufAddress' %}
                            checked
                        {% endif %}
                       data-shipping-address-option-target=".{{ formAddressPrefix }}js-form-meineinkauf-shipping-address"
                >
                <label class="form-check-label" for="{{ formAddressPrefix }}meineinkaufShippingAddressOption">
                    {{ "account.changeMeineinkaufShippingAddress"|trans|sw_sanitize }}
                </label>
            </div>
        </div>

        <div class="{{ formAddressPrefix }}-shipping {{ formAddressPrefix }}js-form-postfiliale-shipping-address d-none">
            {% sw_include '@Storefront/storefront/component/address/address-postfiliale-editor.html.twig' %}
        </div>

        <div class="{{ formAddressPrefix }}-shipping {{ formAddressPrefix }}js-form-packstation-shipping-address d-none">
            {% sw_include '@Storefront/storefront/component/address/address-packstation-editor.html.twig' %}
        </div>

        <div class="{{ formAddressPrefix }}-shipping {{ formAddressPrefix }}js-form-meineinkauf-shipping-address d-none">
            {% sw_include '@Storefront/storefront/component/address/address-meineinkauf-editor.html.twig' %}
        </div>
    {% endif %}

    <div class="{{ formAddressPrefix }}-shipping {{ formAddressPrefix }}js-form-different-shipping-address">
        {% if changeShipping %}
        <div class="card-title k11-mt-20">
            {{ "account.changeNormalShippingAddressHeader"|trans|sw_sanitize }}
        </div>
        {% endif %}

        {% sw_include '@Storefront/storefront/component/address/address-personal.html.twig' with {
            'data': address,
            'prefix': 'address',
            'hideCustomerTypeSelect': changeShipping
        } %}

        {% sw_include '@Storefront/storefront/component/address/address-form.html.twig' with {
            'data': address,
            'prefix': 'address',
            'changeAddress': changeBilling ? 'billingAddress' : 'shippingAddress',
            'displayShippingCountry': changeShipping,
            'resetAddressType': changeShipping
        } %}
    </div>
{% endblock %}

{% block component_address_address_editor_modal_create_address_required_hint %}
    {# Hinweis entfernt im Custom Theme #}
{% endblock %}
