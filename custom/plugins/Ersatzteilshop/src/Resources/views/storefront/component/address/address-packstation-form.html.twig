{% sw_extends '@Storefront/storefront/component/address/address-form.html.twig' %}

{% block component_address_form %}
    {{ block('component_address_form_addressId') }}
    {% block component_address_form_address_type %}
        <input type="hidden" name="{{ prefix }}[customFields][addressType]" value="PackstationAddress" id="{{ prefix }}AddressType">
    {% endblock %}
    {% block component_address_form_address_fields %}

        {#Packstation Number#}
        {{ block('component_address_form_street') }}

        {#Post Number#}
        {{ block('component_address_form_company') }}

        {#Zip Code of Packstation City of your Packstation#}
        <div class="form-row">
            {{ block('component_address_form_zipcode_city') }}
        </div>

        {#Country#}
            {{ block('component_address_form_country') }}

        {#Phone#}
        <div class="form-row">
            {{ block('component_address_form_phone_number') }}
        </div>
    {% endblock %}
{% endblock %}

{% block component_address_form_street %}
    <div class="form-row">
        <div class="form-group col-md-12">
            {% if formViolations.getViolations("/street") is not empty %}
                {% set violationPath = "/street" %}
            {% elseif formViolations.getViolations("/#{prefix}/street") is not empty %}
                {% set violationPath = "/#{prefix}/street" %}
            {% endif %}

            {% block component_address_form_street_label %}

            {% endblock %}

            {% block component_address_form_street_input %}
                <input type="text"
                       class="form-control{% if violationPath %} is-invalid{% endif %}"
                       id="{{ prefix }}AddressStreet"
                       placeholder="{{ "address.packstationNumberPlacehoder"|trans|striptags }}"
                       name="{{ prefix }}[street]"
                       value="{{ data.get('street') }}"
                       required="required"
                       data-form-validation-packstation-number="true"
                       data-form-validation-packstation-number-message="{{ "address.packstationNumberError"|trans|striptags }}"
                >
            {% endblock %}

            {{ block('component_address_form_street_input_error') }}
        </div>
    </div>
{% endblock %}

{% block component_address_form_company %}
    <div class="address-contact-type-company">
        <div class="form-row">
            {% block component_address_form_company_name %}
                <div class="form-group col-12">
                    {% if formViolations.getViolations("/company") is not empty %}
                        {% set violationPath = "/company" %}
                    {% elseif formViolations.getViolations("/#{prefix}/company") is not empty %}
                        {% set violationPath = "/#{prefix}/company" %}
                    {% endif %}

                    {% block component_address_form_company_name_label %}

                    {% endblock %}

                    {% block component_address_form_company_name_input %}
                        <input type="text"
                               class="form-control{% if violationPath %} is-invalid{% endif %}"
                               id="{{ prefix }}company"
                               placeholder="{{ "address.packstationPostNumberPlaceholder"|trans|striptags }}"
                               name="{{ prefix }}[company]"
                               value="{{ data.get('company') }}"
                               required="required"
                               data-form-validation-packstation-post-number="true"
                               data-form-validation-packstation-post-number-message="{{ "address.packstationPostNumberError"|trans|striptags }}"
                        >
                    {% endblock %}

                    {{ block('component_address_form_company_name_input_error') }}
                </div>
            {% endblock %}
        </div>
        <div class="form-row d-none">
            {{ block('component_address_form_company_department') }}
            {{ block('component_address_form_company_vatId') }}
        </div>
    </div>
{% endblock %}

{% block component_address_form_zipcode_city %}
    {% set zipcodeField %}
        {% if formViolations.getViolations("/zipcode") is not empty %}
            {% set violationPath = "/zipcode" %}
        {% elseif formViolations.getViolations("/#{prefix}/zipcode") is not empty %}
            {% set violationPath = "/#{prefix}/zipcode" %}
        {% endif %}

        {% block component_address_form_zipcode_label %}

        {% endblock %}

        <input type="text"
               class="form-control{% if violationPath %} is-invalid{% endif %}"
               id="{% block component_address_form_zipcode_input_id %}{{ prefix }}AddressZipcode{% endblock %}"
               placeholder="{% block component_address_form_zipcode_input_placeholder %}{{ "address.zipcodePlaceholder"|trans|striptags }}{% endblock %} Ihrer Packstation"
               name="{{ prefix }}[zipcode]"
               value="{{ data.get('zipcode') }}"
               required="required">
        {{ block('component_address_form_zipcode_error') }}
    {% endset %}

    {% set cityField %}
        {% if formViolations.getViolations("/city") is not empty %}
            {% set violationPath = "/city" %}
        {% elseif formViolations.getViolations("/#{prefix}/city") is not empty %}
            {% set violationPath = "/#{prefix}/city" %}
        {% else %}
            {% set violationPath = null %}
        {% endif %}

        {% block component_address_form_city_label %}

        {% endblock %}

        <input type="text"
               class="form-control{% if violationPath %} is-invalid{% endif %}"
               id="{% block component_address_form_city_input_id %}{{ prefix }}AddressCity{% endblock %}"
               placeholder="{% block component_address_form_city_input_placeholder %}{{ "address.cityPlaceholder"|trans|striptags }}{% endblock %} der Packstation"
               name="{{ prefix }}[city]"
               value="{{ data.get('city') }}"
               required="required">
        {{ block('component_address_form_city_error') }}
    {% endset %}

    {% if config('core.address.showZipcodeInFrontOfCity') %}
        <div class="form-group col-md-12 col-12">
            {{ zipcodeField }}
        </div>

        <div class="form-group col-md-12 col-12">
            {{ cityField }}
        </div>
    {% else %}
        <div class="form-group col-md-12 col-12">
            {{ cityField }}
        </div>

        <div class="form-group col-md-12 col-12">
            {{ zipcodeField }}
        </div>
    {% endif %}
{% endblock %}

{% block component_address_form_additional_field1 %}{% endblock %}

{% block component_address_form_country_select %}
            <select class="country-select custom-select{% if violationPath %} is-invalid{% endif %}"
                    id="{{ prefix }}AddressCountry"
                    name="{{ prefix }}[countryId]"
                    required="required"
                    data-austria-notice="true"
            >
                {% if not data.get('countryId') %}
                    <option disabled="disabled"
                            value=""
                            selected="selected">
                        {{ "address.countryPlaceholder"|trans|sw_sanitize }}
                    </option>
                {% endif %}
                {% for country in page.countries %}
                    {% if country.shippingAvailable %}
                        <option {% if country.id == data.get('countryId') %} selected="selected"{% endif %}
                                value="{{ country.id }}"
                                data-shipping-available="{{ country.shippingAvailable }}"
                                data-iso-code="{{ country.iso }}"
                        >
                            {{ country.translated.name }}
                        </option>
                    {% endif %}
                {% endfor %}
            </select>
        <div class="form-text austria-notice-text d-none">
            {{ "checkout.noticeAustriaDelivery"|trans|sw_sanitize }}
        </div>
{% endblock %}
