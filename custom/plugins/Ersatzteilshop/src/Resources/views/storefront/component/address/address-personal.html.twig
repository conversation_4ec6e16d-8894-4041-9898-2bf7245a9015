{% sw_extends '@Storefront/storefront/component/address/address-personal.html.twig' %}



{% block component_address_personal_account_type_label %}
    {# Label entfernt im Custom Theme #}
{% endblock %}


{% block component_address_personal_fields_salutation_title %}
    <div class="form-row" style="display: none;">
        {% block component_address_personal_fields_salutation %}
            <div class="form-group col-md-3 col-sm-6">
                {% block component_address_personal_fields_salutation_label %}
                    <label class="form-label"
                           for="{{ idPrefix ~ prefix }}personalSalutation">
                        {{ "account.personalSalutationLabel"|trans|sw_sanitize }}{{ "general.required"|trans|sw_sanitize }}
                    </label>
                {% endblock %}

                {% block component_address_form_salutation_select %}
                    <select id="{{ idPrefix ~ prefix }}personalSalutation"
                            class="custom-select{% if formViolations.getViolations('/salutationId') is not empty %} is-invalid{% endif %}"
                            name="{% if prefix %}{{ prefix }}[salutationId]{% else %}salutationId{% endif %}"
                            required="required">
                        {% for salutation in page.salutations %}
                            <option {% if loop.index == 1 %}
                                selected="selected"
                            {% endif %}
                                value="{{ salutation.id }}">
                                {{ salutation.translated.displayName }}
                            </option>
                        {% endfor %}
                    </select>
                {% endblock %}

                {% block component_address_form_salutation_select_error %}
                    {% if formViolations.getViolations('/salutationId') is not empty %}
                        {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' with {
                            violationPath: '/salutationId'
                        } %}
                    {% endif %}
                {% endblock %}
            </div>
        {% endblock %}

        {% block component_address_personal_fields_title %}
            {% if config('core.loginRegistration.showTitleField') %}
                <div class="form-group col-md-3 col-sm-6">
                    {% block component_address_personal_fields_title_label %}
                        <label class="form-label"
                               for="{{ idPrefix ~ prefix }}personalTitle">
                            {{ "account.personalTitleLabel"|trans|sw_sanitize }}
                        </label>
                    {% endblock %}

                    {% block component_address_personal_fields_title_input %}
                        <input type="text"
                               class="form-control"
                               autocomplete="section-personal title"
                               id="{{ idPrefix ~ prefix }}personalTitle"
                               placeholder="{{ "account.personalTitlePlaceholder"|trans|striptags }}"
                               name="{% if prefix %}{{ prefix }}[title]{% else %}title{% endif %}"
                               value="{{ data.get('title') }}">
                    {% endblock %}
                </div>
            {% endif %}
        {% endblock %}
    </div>
{% endblock %}

{% block component_address_personal_fields_first_name_label %}
    {# Label entfernt im Custom Theme #}
{% endblock %}

{% block component_address_personal_fields_last_name_label %}
    {# Label entfernt im Custom Theme #}
{% endblock %}
