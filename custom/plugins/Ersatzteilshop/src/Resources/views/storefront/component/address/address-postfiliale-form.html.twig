{% sw_extends '@Storefront/storefront/component/address/address-packstation-form.html.twig' %}

{% block component_address_form_address_type %}
    <input type="hidden" name="{{ prefix }}[customFields][addressType]" value="PostfilialeAddress" id="{{ prefix }}AddressType">
{% endblock %}

{% block component_address_form_street_input %}
    <input type="text"
           class="form-control{% if violationPath %} is-invalid{% endif %}"
           id="{{ prefix }}AddressStreet"
           placeholder="{{ "address.postfilialePostalOfficeNumberPlaceholder"|trans|striptags }}"
           name="{{ prefix }}[street]"
           value="{{ data.get('street') }}"
           required="required"
           data-form-validation-postfiliale-number="true"
           data-form-validation-postfiliale-number-message="{{ "address.postfilialeNumberError"|trans|striptags }}"
    >
{% endblock %}

{% block component_address_form_company_name_input %}
    <input type="text"
           class="form-control{% if violationPath %} is-invalid{% endif %}"
           id="{{ prefix }}company"
           placeholder="{{ "address.postfilialePostNumberPlaceholder"|trans|striptags }}"
           name="{{ prefix }}[company]"
           value="{{ data.get('company') }}"
           required="required"
           data-form-validation-postfiliale-post-number="true"
           data-form-validation-postfiliale-post-number-message="{{ "address.postfilialePostNumberError"|trans|striptags }}"
    >
{% endblock %}

{% block component_address_form_zipcode_city %}
{% set zipcodeField %}
    {% if formViolations.getViolations("/zipcode") is not empty %}
        {% set violationPath = "/zipcode" %}
    {% elseif formViolations.getViolations("/#{prefix}/zipcode") is not empty %}
        {% set violationPath = "/#{prefix}/zipcode" %}
    {% endif %}

    {% block component_address_form_zipcode_label %}
        
    {% endblock %}

    <input type="text"
           class="form-control{% if violationPath %} is-invalid{% endif %}"
           id="{% block component_address_form_zipcode_input_id %}{{ prefix }}AddressZipcode{% endblock %}"
           placeholder="{% block component_address_form_zipcode_input_placeholder %}{{ "address.zipcodePlaceholder"|trans|striptags }}{% endblock %} Ihrer Postfiliale"
           name="{{ prefix }}[zipcode]"
           value="{{ data.get('zipcode') }}"
           required="required">
    {{ block('component_address_form_zipcode_error') }}
{% endset %}

    {% set cityField %}
        {% if formViolations.getViolations("/city") is not empty %}
            {% set violationPath = "/city" %}
        {% elseif formViolations.getViolations("/#{prefix}/city") is not empty %}
            {% set violationPath = "/#{prefix}/city" %}
        {% else %}
            {% set violationPath = null %}
        {% endif %}

        {% block component_address_form_city_label %}
            
        {% endblock %}

        <input type="text"
               class="form-control{% if violationPath %} is-invalid{% endif %}"
               id="{% block component_address_form_city_input_id %}{{ prefix }}AddressCity{% endblock %}"
               placeholder="{% block component_address_form_city_input_placeholder %}{{ "address.cityPlaceholder"|trans|striptags }}{% endblock %} der Postfiliale"
               name="{{ prefix }}[city]"
               value="{{ data.get('city') }}"
               required="required">
        {{ block('component_address_form_city_error') }}
    {% endset %}

    {% if config('core.address.showZipcodeInFrontOfCity') %}
        <div class="form-group col-md-12 col-12">
            {{ zipcodeField }}
        </div>

        <div class="form-group col-md-12 col-12">
            {{ cityField }}
        </div>
    {% else %}
        <div class="form-group col-md-12 col-12">
            {{ cityField }}
        </div>

        <div class="form-group col-md-12 col-12">
            {{ zipcodeField }}
        </div>
    {% endif %}
{% endblock %}
