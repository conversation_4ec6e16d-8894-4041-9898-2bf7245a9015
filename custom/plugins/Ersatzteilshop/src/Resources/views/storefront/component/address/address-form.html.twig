{% sw_extends '@Storefront/storefront/component/address/address-form.html.twig' %}

{% block component_address_form %}
    {% block component_address_form_addressId %}
        {% if data.get('id') %}
            <input type="hidden"
                   name="{{ prefix }}[id]"
                   value="{{ data.get('id') }}">
        {% endif %}

    {% endblock %}
    {% block component_address_form_address_type %}
        <input type="hidden" name="{{ prefix }}[addressType]" value="{% if resetAddressType %}{% else %}{{ data.get('customFields').addressType }}{% endif %}" id="{{ prefix }}AddressType">
    {% endblock %}

    {% if not hideCompanyAddressFormRegister %}
        {% block component_address_form_company %}
            {% if config('core.loginRegistration.showAccountTypeSelection') %}
                {% set accounTypeRequired = true %}
            {% endif %}

        {% if config('core.loginRegistration.showAccountTypeSelection') or prefix == "address" or prefix == "shippingAddress" %}
            <div class="{% if prefix == "shippingAddress" or changeAddress == "shippingAddress" %}address-contact-type-company{% elseif prefix == "address" %}js-field-toggle-contact-type-company d-block{% else %}js-field-toggle-contact-type-company d-none{% endif %}">
                {% block component_address_form_company_fields %}
                    <div class="form-row">
                        {% block component_address_form_company_name %}
                            <div class="form-group col-12">
                                {% if formViolations.getViolations("/company") is not empty %}
                                    {% set violationPath = "/company" %}
                                {% elseif formViolations.getViolations("/#{prefix}/company") is not empty %}
                                    {% set violationPath = "/#{prefix}/company" %}
                                {% endif %}

                                    {% block component_address_form_company_name_label %}
                                    {% endblock %}

                                    {% block component_address_form_company_name_input %}
                                        <input type="text"
                                               class="form-control{% if violationPath %} is-invalid{% endif %}"
                                               id="{% block component_address_form_company_name_input_id %}{{ prefix }}company{% endblock %}"
                                               placeholder="{% block component_address_form_company_name_input_placeholder %}{{ "address.companyNamePlaceholder"|trans|striptags }} {% if not hideCompanyOptional %}(optional){% endif %}{% endblock %}"
                                               name="{{ prefix }}[company]"
                                               value="{{ data.get('company') }}"
                                               {% if (prefix == "billingAddress" or changeAddress == "billingAddress") and accounTypeRequired %}required="required"{% endif %}
                                               data-form-validation-max-length="30"
                                               data-form-validation-max-length-message="{{ 'address.maxLengthError'|trans|sw_sanitize }}">
                                    {% endblock %}

                                    {% block component_address_form_company_name_input_error %}
                                        {% if violationPath %}
                                            {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' %}
                                        {% endif %}
                                    {% endblock %}
                                </div>
                            {% endblock %}
                        </div>

                        {% block component_address_form_company_vatId %}
                            {% if showVatIdField %}
                                <div class="form-row">
                                    <div class="form-group col-12">
                                        {% if formViolations.getViolations("/vatId") is not empty %}
                                            {% set violationPath = "/vatId" %}
                                        {% elseif formViolations.getViolations("/#{prefix}/vatId") is not empty %}
                                            {% set violationPath = "/#{prefix}/vatId" %}
                                        {% endif %}

                                        {% block component_address_form_company_vatId_label %}{% endblock %}

                                        {% block component_address_form_company_vatId_input %}
                                            <input type="text"
                                                   class="form-control{% if violationPath %} is-invalid{% endif %}"
                                                   id="{{ idPrefix ~ prefix }}vatId"
                                                   placeholder="{{ "address.companyVatPlaceholder"|trans|striptags }}"
                                                   name="{{ prefix }}[vatId]"
                                                   value="{{ data.get('vatId') }}">
                                        {% endblock %}

                                        {% block component_address_form_company_vatId_input_error %}
                                            {% if violationPath %}
                                                {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' %}
                                            {% endif %}
                                        {% endblock %}
                                    </div>
                                </div>
                            {% endif %}
                        {% endblock %}
                    {% endblock %}
                </div>
            {% endif %}
        {% endblock %}
    {% endif %}

    <div class="form-row">
        {% block component_address_form_phone_number %}
            {% if config('core.loginRegistration.showPhoneNumberField') %}
                <div class="form-group col-md-12">
                    {% if formViolations.getViolations("/phoneNumber") is not empty %}
                        {% set violationPath = "/phoneNumber" %}
                    {% elseif formViolations.getViolations("/#{prefix}/phoneNumber") is not empty %}
                        {% set violationPath = "/#{prefix}/phoneNumber" %}
                    {% endif %}

                    {% block component_address_form_phone_number_input %}
                        <input type="text"
                               class="form-control"
                               id="{% block component_address_form_phone_number_input_id %}{{ prefix }}AddressPhoneNumber{% endblock %}"
                               placeholder="{{ "address.phoneNumberPlaceholder"|trans|striptags }}"
                               name="{{ prefix }}[phoneNumber]"
                               value="{{ data.get('phoneNumber') }}"
                               data-form-validation-phone-number="true"
                               data-form-validation-phone-number-message="{{ "address.phoneNumberError"|trans|sw_sanitize }}"
                            {{ config('core.loginRegistration.phoneNumberFieldRequired') ? 'required="true"' }}>
                    {% endblock %}

                    {% block component_address_form_phone_error %}
                        {% if violationPath %}
                            {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' %}
                        {% endif %}
                    {% endblock %}
                </div>
            {% endif %}
        {% endblock %}
    </div>

    {% block component_address_form_address_fields %}
        <div class="form-row">
            {% block component_address_form_street %}
                <div class="form-group col-md-12">
                    {% if formViolations.getViolations("/street") is not empty %}
                        {% set violationPath = "/street" %}
                    {% elseif formViolations.getViolations("/#{prefix}/street") is not empty %}
                        {% set violationPath = "/#{prefix}/street" %}
                    {% endif %}

                    {% block component_address_form_street_input %}
                        <input type="text"
                               class="form-control{% if violationPath %} is-invalid{% endif %}"
                               id="{% block component_address_form_street_input_id %}{{ prefix }}AddressStreet{% endblock %}"
                               placeholder="{% block component_address_form_street_input_placeholder %}{{ "address.streetPlaceholder"|trans|striptags }}{% endblock %}"
                               name="{{ prefix }}[street]"
                               value="{{ data.get('street') }}"
                               required="required"
                               data-form-validation-street="true"
                               data-form-validation-street-message="{{ "address.streetError"|trans|sw_sanitize }}"
                               data-form-validation-max-length="30"
                               data-form-validation-max-length-message="{{ 'address.maxLengthError'|trans|sw_sanitize }}">
                    {% endblock %}

                    {% block component_address_form_street_input_error %}
                        {% if violationPath %}
                            {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' %}
                        {% endif %}
                    {% endblock %}
                </div>
            {% endblock %}
        </div>
        <div class="form-row">
            {% block component_address_form_zipcode_city %}
                {% set zipcodeField %}
                    {% if formViolations.getViolations("/zipcode") is not empty %}
                        {% set violationPath = "/zipcode" %}
                    {% elseif formViolations.getViolations("/#{prefix}/zipcode") is not empty %}
                        {% set violationPath = "/#{prefix}/zipcode" %}
                    {% endif %}

                    {% block component_address_form_zipcode_input %}
                        <input type="text"
                               class="form-control{% if violationPath %} is-invalid{% endif %}"
                               id="{% block component_address_form_zipcode_input_id %}{{ prefix }}AddressZipcode{% endblock %}"
                               placeholder="{% block component_address_form_zipcode_input_placeholder %}{{ "address.zipcodePlaceholder"|trans|striptags }}{% endblock %}"
                               name="{{ prefix }}[zipcode]"
                               value="{{ data.get('zipcode') }}"
                               required="required">
                    {% endblock %}

                    {% block component_address_form_zipcode_error %}
                        {% if violationPath %}
                            {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' %}
                        {% endif %}
                    {% endblock %}
                {% endset %}

                {% set cityField %}
                    {% if formViolations.getViolations("/city") is not empty %}
                        {% set violationPath = "/city" %}
                    {% elseif formViolations.getViolations("/#{prefix}/city") is not empty %}
                        {% set violationPath = "/#{prefix}/city" %}
                    {% else %}
                        {% set violationPath = null %}
                    {% endif %}

                    {% block component_address_form_city_input %}
                        <input type="text"
                               class="form-control{% if violationPath %} is-invalid{% endif %}"
                               id="{% block component_address_form_city_input_id %}{{ prefix }}AddressCity{% endblock %}"
                               placeholder="{% block component_address_form_city_input_placeholder %}{{ "address.cityPlaceholder"|trans|striptags }}{% endblock %}"
                               name="{{ prefix }}[city]"
                               value="{{ data.get('city') }}"
                               required="required">
                    {% endblock %}

                    {% block component_address_form_city_error %}
                        {% if violationPath %}
                            {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' %}
                        {% endif %}
                    {% endblock %}
                {% endset %}

                {% block component_address_form_zipcode_city_group %}
                    {% if config('core.address.showZipcodeInFrontOfCity') %}
                        <div class="form-group col-md-6 col-6 register-zipcode-field">
                            {{ zipcodeField }}
                        </div>

                        <div class="form-group col-md-6 col-6">
                            {{ cityField }}
                        </div>
                    {% else %}
                        <div class="form-group col-md-4 col-8">
                            {{ cityField }}
                        </div>

                        <div class="form-group col-md-2 col-4">
                            {{ zipcodeField }}
                        </div>
                    {% endif %}
                {% endblock %}
            {% endblock %}
        </div>

            {% block component_address_form_country %}
                <div class="form-row">
                    <div class="form-group col-md-12">
                        {% if formViolations.getViolations("/countryId") is not empty %}
                            {% set violationPath = "/countryId" %}
                        {% elseif formViolations.getViolations("/#{prefix}/countryId") is not empty %}
                            {% set violationPath = "/#{prefix}/countryId" %}
                        {% endif %}

                        {% block component_address_form_country_select %}
                            <select class="country-select custom-select{% if violationPath %} is-invalid{% endif %}"
                                    id="{% block component_address_form_country_select_id %}{{ prefix }}AddressCountry{% endblock %}"
                                    name="{{ prefix }}[countryId]"
                                    required="required"
                                    data-austria-notice="true"
                                    {% if changeAddress == 'billingAddress' %}
                                    data-shipping-not-available-notice="true"
                                     {% endif %}
                                    >
                                {% if not data.get('countryId') %}
                                    <option disabled="disabled"
                                            value=""
                                            selected="selected">
                                        {{ "address.countryPlaceholder"|trans|sw_sanitize }}
                                    </option>
                                {% endif %}
                                {% for country in page.countries %}
                                    {% if not displayShippingCountry or (displayShippingCountry == true and country.shippingAvailable) %}
                                        <option {% if country.id == data.get('countryId') %} selected="selected"{% endif %}
                                                value="{{ country.id }}"
                                                data-shipping-available="{{ country.shippingAvailable }}"
                                                data-iso-code="{{ country.iso }}"
                                        >
                                            {{ country.translated.name }}
                                        </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        {% endblock %}
                            <div class="form-text austria-notice-text d-none">
                                {{ "checkout.noticeAustriaDelivery"|trans|sw_sanitize }}
                            </div>
                        {# {% if prefix == "billingAddress" or changeAddress == 'billingAddress' %} #}
                            <div class="form-text js-shipping-not-available d-none">
                                {{ "checkout.noticeNotSupportShipping"|trans|sw_sanitize }}
                            </div>

                            <div class="form-text js-shipping-not-available-in-billing d-none">
                                {{ "checkout.noticeNotSupportShippingInBilling"|trans|sw_sanitize }}
                            </div>


                        {# {% endif %} #}
                    </div>
                </div>

                {% block component_address_form_shipping_not_available_notice %}
                    {% if prefix == "billingAddress" %}
                    <div class="form-row">
                        <div class="form-group col-md-12 js-ch-shipping-option warning d-none">
                            <label class="form-label" for="chShippingOption">
                                {{ "address.switzerlandShippingAddressOption"|trans }}
                            </label>

                            <select id="chShippingOptions" class="form-control-md-6 custom-select">
                                <option value="differentShippingAddress">
                                    {{ "address.shipToGermanOption"|trans }}
                                </option>
                                <option value="meineinkaufAddress" selected="selected">
                                    {{ "address.shipToMeineinkaufOption"|trans }}
                                </option>
                            </select>

                            <div class="form-text">
                                {{ "address.switzerlandShippingAddressDesc"|trans|raw }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                {% endblock %}
            {% endblock %}

        {% block component_address_form_additional_field1 %}
            {% if prefix == "billingAddress" or enableMeineinkauf %}
            <div class="form-row">
                <div class="form-group col-md-12 js-meineinkauf-email warning {% if prefix == "billingAddress" %}d-none"{% endif %}>
                    {% block component_address_form_additional_field1_label %}
                        <label class="form-label"
                               for="meineinkaufEmail">
                            {{ "address.meineinkaufEmailLabel"|trans|sw_sanitize }}{{ "general.required"|trans|sw_sanitize }}
                        </label>
                    {% endblock %}

                    {% block component_address_form_additional_field1_input %}
                        <input type="email"
                               class="form-control form-control-md-6"
                               id="meineinkaufEmail"
                               placeholder="{{ "address.meineinkaufEmailPlaceholder"|trans|striptags }}"
                               name="{{ prefix }}[additionalAddressLine1]"
                               value="{{ data.get('additionalAddressLine1') }}"
                               data-form-validation-meineinkauf="true"
                               data-private-email-label="{{ "address.meineinkaufPrivateEmail"|trans|striptags }}"
                               {% if enableMeineinkauf %}required="required"{% endif %}
                        >
                    {% endblock %}

                    <div class="form-text js-validation-message">
                        {{ "address.meineinkaufEmailError"|trans|striptags }}
                    </div>
                </div>
            </div>
            {% endif %}
        {% endblock %}
    {% endblock %}
{% endblock %}
