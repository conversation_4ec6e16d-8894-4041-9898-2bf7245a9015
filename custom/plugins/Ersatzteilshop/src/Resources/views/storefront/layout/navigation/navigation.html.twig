{% sw_extends "@Storefront/storefront/layout/navigation/navigation.html.twig" %}

{% set householdMenu = page.header.extensions.householdMenu.menu.elements %}
{% set kitchenMenu = page.header.extensions.kitchenMenu.menu.elements %}
{% set eMobilityMenu = page.header.extensions.emobilityMenu.menu.elements %}
{% block layout_main_navigation_menu %}
    <nav class="nav main-navigation-menu" data-header-navigation="true">
        {% block layout_main_navigation_menu_household %}
            {% sw_include '@Storefront/storefront/layout/navigation/navigation_link.html.twig' with {
                'url': '#',
                'title': 'general.navigation.household'|trans,
                'icon': 'bundles/ersatzteilshop/assets/icons/Asset_icon_household.svg',
                'name': 'general.navigation.household'|trans,
                'tag': 'household',
                'items': householdMenu
            } %}
        {% endblock %}

        {% block layout_main_navigation_menu_kitchen %}
            {% sw_include '@Storefront/storefront/layout/navigation/navigation_link.html.twig' with {
                'url': '#',
                'title': 'general.navigation.kitchen'|trans,
                'icon': 'bundles/ersatzteilshop/assets/icons/Asset_icon_kitchen.svg',
                'name': 'general.navigation.kitchen'|trans,
                'tag': 'kitchen',
                'items': kitchenMenu
            } %}
        {% endblock %}

        {% block layout_main_navigation_cleaning %}
            {% sw_include '@Storefront/storefront/layout/navigation/navigation_link.html.twig' with {
                'url': '#',
                'title': 'general.navigation.eMobility'|trans,
                'icon': 'bundles/ersatzteilshop/assets/icons/Asset_icon_eMobility.svg',
                'name': 'general.navigation.eMobility'|trans,
                'tag': 'cleaning',
                'items': eMobilityMenu
            } %}
        {% endblock %}

        {% block layout_main_navigation_all_appliances %}
            {% sw_include '@Storefront/storefront/layout/navigation/navigation_link.html.twig' with {
                'url': seoUrl('frontend.appliance.group.overview.page'),
                'title': 'general.navigation.allAppliances'|trans,
                'icon': 'bundles/ersatzteilshop/assets/icons/Asset_icon_appliances.svg',
                'name': 'general.navigation.allAppliances'|trans,
                'target':'_self'
            } %}
        {% endblock %}

        {% block layout_main_navigation_videos %}
            {% sw_include '@Storefront/storefront/layout/navigation/navigation_link.html.twig' with {
                'url': path('frontend.video.library.overview'),
                'title': 'general.navigation.videos'|trans,
                'icon': 'bundles/ersatzteilshop/assets/icons/Asset_icon_videos.svg',
                'name': 'general.navigation.videos'|trans,
                'target':'_self'
            } %}
        {% endblock %}

        {% block layout_main_navigation_repair_instructions %}
            {% sw_include '@Storefront/storefront/layout/navigation/navigation_link.html.twig' with {
                'url': seoUrl('frontend.navigation.page', {'navigationId': '9679c396f2084b098aff653efee92683'}),
                'title': 'general.navigation.repairInstructions'|trans,
                'icon': 'bundles/ersatzteilshop/assets/icons/Asset_icon_repair_instructions.svg',
                'name': 'general.navigation.repairInstructions'|trans,
                'target':'_self'
            } %}
        {% endblock %}

        {% block layout_main_navigation_forum %}
            {% sw_include '@Storefront/storefront/layout/navigation/navigation_link.html.twig' with {
                'url': 'https://forum.ersatzteilshop.de/',
                'title': 'general.navigation.forum'|trans,
                'icon': 'bundles/ersatzteilshop/assets/icons/Asset_icon_forum.svg',
                'name': 'general.navigation.forum'|trans,
                'target':'_blank'
            } %}
        {% endblock %}

        {% block layout_main_navigation_faq_help %}
            {% sw_include '@Storefront/storefront/layout/navigation/navigation_link.html.twig' with {
                'url': seoUrl('frontend.helpcenter.overview'),
                'title': 'general.navigation.faq'|trans,
                'icon': 'bundles/ersatzteilshop/assets/icons/Asset_icon_faq.svg',
                'name': 'general.navigation.faq'|trans,
                'target':'_self'
            } %}
        {% endblock %}
    </nav>
{% endblock %}

{% block layout_main_navigation_menu_flyout_wrapper %}
{% endblock %}
