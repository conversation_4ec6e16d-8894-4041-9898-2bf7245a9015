{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_stylesheet %}
    {% if isHMRMode %}
        {# CSS will be loaded from the JS automatically #}
    {% else %}
        {% include '@Ersatzteilshop/storefront/layout/styles.html.twig' %}
    {% endif %}
{% endblock %}

{% block layout_head_meta_tags_general %}
    <meta name="author"
          content="{{ metaInformation.author|striptags }}"/>
    <meta name="robots" content="index, follow"/>
    <meta name="revisit-after"
          content="{{ metaInformation.revisit|striptags }}"/>
    <meta name="keywords"
          content="{% block layout_head_meta_tags_keywords %}{{ metaKeywords }}{% endblock %}"/>
    <meta name="description"
          content="{% block layout_head_meta_tags_description %}{{ metaDescription }}{% endblock %}"/>
{% endblock %}

{% block layout_head_meta_tags_opengraph %}
    <meta property="og:type"
          content="product"/>
    <meta property="og:site_name"
          content="{{ config('core.basicInformation.shopName') }}"/>
    <meta property="og:url"
          content="{{ seoUrl('frontend.detail.page', { productId: page.product.id }) }}"/>
    <meta property="og:title"
          content="{{ metaTitle }}"/>

    <meta property="og:description"
          content="{{ metaDescription }}"/>
    <meta property="og:image"
          content="{{ page.product.cover.media.url }}"/>

    {% if page.product.manufacturer %}
        <meta property="product:brand"
              content="{{ page.product.manufacturer.translated.name }}"/>
    {% endif %}
    <meta property="product:price"
          content="{{ page.product.calculatedPrice.unitPrice|currency }}"/>
    <meta property="product:product_link"
          content="{{ seoUrl('frontend.detail.page', { productId: page.product.id }) }}"/>

    <meta name="twitter:card"
          content="product"/>
    <meta name="twitter:site"
          content="{{ config('core.basicInformation.shopName') }}"/>
    <meta name="twitter:title"
          content="{{ metaTitle }}"/>
    <meta name="twitter:description"
          content="{{ metaDescription }}"/>
    <meta name="twitter:image"
          content="{{ page.product.cover.media.url }}"/>
{% endblock %}

{% block layout_head_meta_tags %}
    {{ parent() }}
    {% set cdnUrl = app.request.server.get('CDN_URL')~'/public/' %}
    {% set src = page.product.cover.media.url|replace({(cdnUrl): cloud_flare_image_url~ 'md/'}) %}
    <link rel="preload" href="{{ src }}" as="image">
{% endblock %}

{% block layout_head_title_inner %}
    {% set title %}{% apply spaceless %}
        {{ page.product.translated.name }} - {{ page.product.seoCategory.name }}{% if page.product.variation %} - {% for variation in page.product.variation %}{{ variation.group }}: {{ variation.option }}{% if page.product.variation|last != variation %}{{ " | " }}{% endif %}{% endfor %}{% endif %}
    {% endapply %}
    {% endset %}
    {% set title = title|u.truncate(70, '', false)  %}
    {% if title|length > 70 %}
        {% set title = title|u.beforeLast(' ')  %}
    {% endif %}
    {{ title }}
{% endblock %}

{% block layout_head_canonical %}
    <link rel="canonical" href="{{ seoUrl('frontend.detail.page', { productId: page.product.id }) }}" />
{% endblock %}
