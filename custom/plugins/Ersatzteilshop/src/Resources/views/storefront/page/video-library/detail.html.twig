{% sw_extends '@Storefront/storefront/page/_page.html.twig' %}

{% set deviceType = app.request.headers.get('User-Agent')|lower %}

{% if 'mobile' in deviceType %}
    {% set height = '215' %}
{% else %}
    {% set height = '551' %}
{% endif %}

{% set defaultTranslation = null %}
{% if video.translations is defined and video.translations is not empty %}
    {% set defaultTranslation = video.translations.first %}
{% endif %}

{% set videoObjectMetadata = defaultTranslation.videoObjectMetadata ?? null %}
{% set faqMetaData = defaultTranslation.faqMetaData ?? null %}

{% block base_head %}
    {% sw_include '@Storefront/storefront/page/video-library/meta.html.twig' %}

    {# Render structured data schemas generated by VideoLibrarySchemaService #}
    {% if page.schemas %}
        <script type="application/ld+json">
        {{ page.schemas.vars|json_encode(constant('JSON_PRETTY_PRINT') b-or constant('JSON_UNESCAPED_SLASHES'))|raw }}
        </script>
    {% endif %}

{% endblock %}

{% block base_main_inner %}
    <div class="container" data-attribute-video-library-detail>
        {% block base_main_container %}
            
            <div class="container-main pt-5" >
                {% block page_content %}
                    
                    <main class="content-main-video">
                        {% block base_content_heading %}
                            {% block page_breadcrumb %}
                                <ul class="k11-pt-0-important breadcrumb">
                                    <li class="breadcrumb-item" >
                                        <a href="{{ seoUrl('frontend.home.page') }}"><span>{{ 'ersatzteilshop.breadcrumb.home'|trans|sw_sanitize }}</span></a>
                                    </li>
                                    <li class="breadcrumb-item" >
                                        <a href="{{ seoUrl('frontend.video.library.overview') }}"><span>Reparatur</span></a>
                                    </li>
                                    <li class="breadcrumb-item" >
                                        <span>{{ video.questionInput ?: video.title }}</span>
                                    </li>
                                </ul>
                            {% endblock %}
                            <header class="video-header">
                                
                                <h1>{{ video.title }}</h1>
                            </header>
                            {% if video.fakeUpdatedAt %}
                                <p>{{ video.fakeUpdatedAt|date("d.m.Y") }}</p>
                            {% elseif video.updatedAt  %}
                                <p>{{ video.updatedAt|date("d.m.Y") }}</p>
                            {% else  %}
                                <p>{{ video.createdAt|date("d.m.Y") }}</p>
                            {% endif %}
                        {% endblock %}
                        
                        {% block video_library %}
                            {% include '@Ersatzteilshop/storefront/page/video-library/youtube-video.html.twig' with {
                                height: height,
                                youtubeId: video.youtubeId,
                                video: video
                            } only %}
                        {% endblock %}
                        
                        {% block video_library_interactions %}
                            {% sw_include '@Ersatzteilshop/storefront/page/video-library/partials/_interactions.html.twig' %}
                        {% endblock %}
                        
                        {% block video_library_request_banner %}
                            {% sw_include '@Ersatzteilshop/storefront/page/video-library/partials/_request-banner.html.twig' %}
                        {% endblock %}
                        
                        {% block video_library_description %}
                            <div class="k11-pb-20">
                                {{ video.description|raw }}
                            </div>
                            
                            {% sw_include '@Ersatzteilshop/storefront/page/video-library/partials/_howto.html.twig' %}
                            
                            {% sw_include '@Ersatzteilshop/storefront/page/video-library/partials/_faq.html.twig' %}
                            
                            {% sw_include '@Ersatzteilshop/storefront/page/video-library/partials/_comments.html.twig' %}
                            
                            {% if page.relatedVideos|length > 0 %}
                                <section class="related-videos w-questions-section k11-my-40">
                                    <h2 class="related-videos__title mb-4">
                                        {{ 'video-library.overview.wQuestions'|trans|sw_sanitize }}
                                    </h2>
                                    
                                    <div class="related-videos__search k11-mb-20" style="display: none" data-search-container>
                                        <form class="k11-border-active d-flex align-items-center">
                                            <input type="search"
                                                   class="input-group search-input border-0 k11-border-radius-8 k11-pl-15 form-control"
                                                   placeholder="Video suchen..."
                                                   autocomplete="off"
                                                   autocapitalize="off"
                                                   maxlength="30"
                                                   data-search-input>
                                            <div class="svg-container">
                                                <button class="search-input--btn border-0 p-0 outline-none bg-none" type="button" disabled>
                                                    <svg class="k11-p-2" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <rect width="34" height="34" rx="5" fill="#202E3D"/>
                                                        <path d="M5 27.1333L13.4 18.7333C12.8667 18.0667 12.4444 17.3 12.1333 16.4333C11.8222 15.5667 11.6667 14.6444 11.6667 13.6667C11.6667 11.2444 12.5056 9.19444 14.1833 7.51667C15.8611 5.83889 17.9111 5 20.3333 5C22.7556 5 24.8056 5.83889 26.4833 7.51667C28.1611 9.19445 29 11.2444 29 13.6667C29 16.0889 28.1611 18.1389 26.4833 19.8167C24.8056 21.4944 22.7556 22.3333 20.3333 22.3333C19.3556 22.3333 18.4333 22.1778 17.5667 21.8667C16.7 21.5556 15.9333 21.1333 15.2667 20.6L6.86667 29L5 27.1333ZM14.3333 13.6667C14.3333 15.3333 14.9167 16.75 16.0833 17.9167C17.25 19.0833 18.6667 19.6667 20.3333 19.6667C22 19.6667 23.4167 19.0833 24.5833 17.9167C25.75 16.75 26.3333 15.3333 26.3333 13.6667C26.3333 12 25.75 10.5833 24.5833 9.41667C23.4167 8.25 22 7.66667 20.3333 7.66667C18.6667 7.66667 17.25 8.25 16.0833 9.41667C14.9167 10.5833 14.3333 12 14.3333 13.6667Z" fill="white"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                    
                                    <div class="related-videos__grid row k11-mr-0">
                                        {% for video in page.relatedVideos %}
                                            <div class="related-videos__item col-12 col-lg-4 k11-mb-20 {% if loop.index > 12 %}d-none{% endif %}">
                                                <div class="question-input-container">
                                                    <a class="k11-black-link k11-font-weight-500"
                                                       href="{{ seoUrl('frontend.video.library.page', {'videoLibraryId': video.id}) }}">
                                                        <u>{{ video.translated.questionInput ?: video.translated.title }}</u>
                                                    </a>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                    
                                    {% if page.relatedVideos|length > 12 %}
                                        <div class="related-videos__actions">
                                            <button class="load-more-videos" data-load-more>
                                                {{ 'video-library.overview.loadAll'|trans|sw_sanitize }}
                                            </button>
                                        </div>
                                    {% endif %}
                                </section>
                            {% endif %}
                            
                            {% sw_include '@Ersatzteilshop/storefront/component/widgets/modelnumber-scanner-widget/modelnumber-scanner-widget.html.twig' %}
                            
                            <div class="py-2">
                                {% if video.tags %}
                                    {% for tag in video.tags %}
                                        <span class="videoTag">{{ tag|upper }}</span>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        {% endblock %}
                    </main>
                {% endblock %}
            </div>
        {% endblock %}
    </div>
    
    {% block bottom_container %}{% endblock %}
{% endblock %}