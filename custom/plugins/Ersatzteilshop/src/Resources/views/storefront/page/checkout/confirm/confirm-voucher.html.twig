{% block page_checkout_vouchercode_container %}
    <h3 class="card-title" style="padding-bottom: 0px !important;">
        {{ "checkout.voucherHeading"|trans|sw_sanitize }}
    </h3>
    {% block page_checkout_confirm_address_shipping_data_equal %}

        <form action="{{ path('frontend.checkout.promotion.add') }}"
              class="cart-add-promotion"
              data-form-csrf-handler="true"
              method="post">

            {% block page_checkout_cart_add_promotion_csrf %}
                {{ sw_csrf('frontend.checkout.promotion.add') }}
            {% endblock %}

            {% block page_checkout_cart_add_promotion_forward %}
                <input type="hidden"
                       name="forwardTo"
                       value="frontend.checkout.confirm.page">
            {% endblock %}

            {% block page_checkout_cart_add_promotion_input_group %}
                    {% block page_checkout_cart_add_promotion_label %}

                    {% endblock %}

                    {% block page_checkout_cart_add_promotion_input %}
                    <div class="promotion-input-group voucher-input-wrapper d-flex">
                            <input type="text"
                                   name="code"
                                   class="form-control col-md-6 col-sm-12 voucher-input-enhanced"
                                   id="addPromotionInput"
                                   aria-label="{{ "checkout.addPromotionLabel"|trans|striptags }}"
                                   aria-describedby="addPromotion"
                                   required="required"
                                   placeholder="Code hier eingeben"
                                   data-voucher-enhancement="true">
                            <div class="voucher-error-message" style="display: none;">
                                <small class="text-danger">Dieser Code ist ungültig</small>
                            </div>
                    {% endblock %}

                    {% block page_checkout_cart_add_promotion_submit %}
                        <button class="btn checkout-button-grey hover-light-grey voucher-submit-enhanced"
                                type="submit"
                                id="addPromotion">
                            {{ "checkout.voucherButton"|trans|sw_sanitize }}
                        </button>
                    </div>
                    {% endblock %}
            {% endblock %}
        </form>
    {% endblock %}

{% endblock %}
