{% sw_extends '@Storefront/storefront/page/checkout/confirm/index.html.twig' %}

{% block base_head %}
    {% sw_include '@Storefront/storefront/page/checkout/cart/meta.html.twig' %}
{% endblock %}

{% block base_header %}
    {% sw_include '@Storefront/storefront/layout/header/header-minimal-checkout.html.twig' %}
{% endblock %}

{% block page_checkout_confirm %}
    {{ block('page_checkout_confirm_alerts') }}
    {{ block('page_checkout_confirm_header') }}

    <div data-confirm-payment-toggle="true">
        {% block page_checkout_confirm_payment %}
            <div class="checkout-confirm-step-one">
                {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-payment.html.twig' %}
            </div>
        {% endblock %}

        <div class="checkout-confirm-step-two">
            {{ block('page_checkout_confirm_product_table') }}
            {{ block('page_checkout_confirm_address') }}
            {{ block('page_checkout_confirm_hidden_line_items_information') }}
        </div>
    </div>
{% endblock %}

{% block page_checkout_confirm_alerts %}
    {% block page_checkout_confirm_violations %}
        {% for violation in formViolations.getViolations() %}
            {% set snippetName = "error.#{violation.code}" %}
            {% set fieldName = violation.propertyPath|trim('/', 'left') %}

            {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                type: "danger",
                content: snippetName|trans({'%field%': fieldName})|sw_sanitize
            } %}
        {% endfor %}
    {% endblock %}
    {% block page_checkout_confirm_errors %}
        <div class="unzer-payment--error-wrapper" hidden="hidden">
            {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                type: "danger",
                content: ""
            } %}
        </div>
    {% endblock %}
{% endblock %}

{% block page_checkout_confirm_header %}

{% endblock %}



{% block page_checkout_confirm_product_table %}
    <div class="confirm-product k11-mt-40">
        {% block page_checkout_confirm_table_container %}
            <h2 class="confirm-section-title">{{ 'checkout.confirmProduct'|trans|sw_sanitize }}</h2>
            <div class="card">
                <div class="card-body">
                    {% block page_checkout_confirm_table_header %}
                        {% sw_include '@Storefront/storefront/page/checkout/cart/cart-product-header.html.twig' %}
                    {% endblock %}

                    {% block page_checkout_confirm_table_items %}
                        {% for lineItem in page.cart.lineItems %}
                            {% if not isTip(lineItem) %}
                                {% block page_checkout_confirm_table_item %}
                                    {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-item.html.twig' %}
                                {% endblock %}
                            {% endif %}
                        {% endfor %}
                    {% endblock %}
                </div>
            </div>
        {% endblock %}
    </div>
{% endblock %}


{% block page_checkout_confirm_address %}
    {# Adress-Container #}
    <h2 class="confirm-section-title">Adresse prüfen</h2>
    <div class="confirm-address">
        {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-address.html.twig' %}
    </div>

    {# Payment und Voucher Container #}
    <div class="confirm-payment-voucher-wrapper">
        <div class="confirm-payment-wrapper mt-4 mt-md-0">
            <h2 class="section-title-outside">Zahlungsart prüfen</h2>
            <div class="confirm-payment-container">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        {{ "checkout.paymentHeading"|trans|sw_sanitize }}
                    </h3>
                        <img id="svg-back-to-payment" alt="edit-note" style="position: relative; top: -10px; cursor: pointer" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/edit-note.svg') }}">
                </div>

                {% set selectedPayment = context.paymentMethod %}

                <div class="selected-payment-method k11-mt-20 d-flex align-items-center gap-2" data-payment-method-id="{{ selectedPayment.id }}">
                    {% if selectedPayment.media %}
                        <img src="{{ selectedPayment.media.url }}"
                             alt="{{ selectedPayment.media.translated.alt ?: selectedPayment.translated.name }}"
                             title="{{ selectedPayment.media.translated.title ?: selectedPayment.translated.name }}"
                             class="payment-method-image"
                             style="max-width: 50px; height: auto;" />
                    {% endif %}

                    <span class="payment-method-name">
                        {{ selectedPayment.translated.name }}
                    </span>
                </div>
            </div>
        </div>

        <div class="confirm-voucher-wrapper mt-4 mt-md-0">
            <h2 class="section-title-outside">Gutschein einlösen</h2>
            <div class="confirm-voucher-container">
                {% block page_checkout_vouchcode %}
                    <div class="confirm-voucher">
                        <div class="card-body">
                            {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-voucher.html.twig' %}
                        </div>
                    </div>
                {% endblock %}
            </div>
        </div>
    </div>

    {% if tipProductId %}
        <div class="tip-container k11-md-p-20 k11-p-10 k11-mb-20 k11-mt-40" data-tipping="true">
            {% set tip = 0 %}
            {% set total = page.cart.price.totalPrice %}
            {% for lineItem in page.cart.lineItems %}
                {% if isTip(lineItem) %}
                    {% set tip = lineItem.price.totalPrice %}
                    {% set total = total - tip %}
                {% endif %}
            {% endfor %}
            <label class="k11-checkbox-container k11-gap-10">
                <input type="checkbox" class="js-tip-checkbox" {% if tip %}checked{% endif %}>
                <span class="k11-checkbox"></span>
                <div>
                    <div class="prio-label">Dankeschön an das Team</div>
                    <div>Mehr als nur Ersatzteile</div>
                </div>
            </label>
            <div class="tip-grid">
                <span class="tip-item">Reparaturvideos</span>
                <span class="tip-item">Fehleranalyse</span>
                <span class="tip-item">Technikerforum</span>
                <span class="tip-item">WhatsApp</span>
                <span class="tip-item">Telefonsupport</span>
                <span class="tip-item">YouTube</span>
            </div>
            <div class="tip-amount-container">
                {% set hit = false %}
                {% set formAjaxSubmitOptions = {
                    reloadOnFinish: true
                } %}
                {% for percentage in [2, 4, 6] %}
                    <form method="POST" action="{{ path('frontend.checkout.tip') }}" data-form-csrf-handler="true" data-form-ajax-submit="true" data-form-ajax-submit-options='{{ formAjaxSubmitOptions|json_encode }}'>
                        {{ sw_csrf('frontend.checkout.tip') }}
                        <input type="hidden" name="amount" value="{{ (total * percentage/100)|round(2) }}">
                        <input type="hidden" name="redirectTo" value="frontend.checkout.confirm.page">
                        <div role="button" class="tip-amount js-tip-amount
                        {% if (tip - ((total * percentage/100)|round(2)))|abs < 0.01 %}
                            {% set hit = true %}
                            tip-selected
                        {% endif %}">
                            <span class="tip-amount-percentage">{{ percentage }}%</span>
                            <span class="tip-amount-absolute">{{ (total * percentage/100)|round(2)|currency }}</span>
                        </div>
                    </form>
                {% endfor %}
                <form method="POST" action="{{ path('frontend.checkout.tip') }}" class="tip-amount-custom js-tip-amount-custom{% if not hit and tip %} tip-selected{% endif %}" data-form-csrf-handler="true" data-form-ajax-submit="true" data-form-ajax-submit-options='{{ formAjaxSubmitOptions|json_encode }}'>
                    {{ sw_csrf('frontend.checkout.tip') }}
                    <input type="hidden" name="redirectTo" value="frontend.checkout.confirm.page">
                    <input name="amount" type="text" autocomplete="off" placeholder="Eigener Betrag 0,00" {% if not hit and tip %}value="{{ tip|number_format(2, ',', '.') }}"{% endif %}>
                    <div class="suffix">&euro;</div>
                </form>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block base_footer %}
    <footer class="footer-main">
        {% block base_footer_inner %}
            {% sw_include '@Storefront/storefront/layout/footer/scroll_up_bar.html.twig' %}
            {% sw_include '@Storefront/storefront/layout/footer/footer.html.twig' %}
        {% endblock %}
    </footer>

{% endblock %}

{% block page_checkout_additional %}
{% endblock %}

{% block page_checkout_aside_actions %}{% endblock %}

 {% block page_checkout_confirm_shipping %}
     <div class="col-sm-6 confirm-shipping">
         {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-shipping.html.twig' %}
     </div>
 {% endblock %}


{% block page_checkout_aside %}
<div class="checkout-main">
    <div class="page-checkout-aside">

        {{ parent() }}

            {{ block('page_checkout_confirm_tos') }}


        {% block page_checkout_confirm_order_bottom %}
            <div class="checkout-action text-center">
                <div class="checkout-buttons">

                    {% block page_checkout_confirm_order_form %}
                        <form id="confirmOrderForm"
                              action="{{ path('frontend.checkout.finish.order') }}"
                              data-form-csrf-handler="true"
                              data-form-preserver="true"
                              data-form-submit-loader="true"
                              method="post">

                            {% block page_checkout_aside_actions_csrf %}
                                {{ sw_csrf('frontend.checkout.finish.order') }}
                            {% endblock %}

                            <a href="{{ path('frontend.checkout.cart.page') }}" class="btn button_fix checkout-register-back hover-light-grey k11-mr-10">
                                {{ "checkout.returnToCart"|trans|sw_sanitize }}
                            </a>

                            {% block page_checkout_confirm_form_submit %}
                                <button id="confirmFormSubmit"
                                        class="btn btn-primary button_fix float-right checkout-register-submit hover-light-green"
                                        form="confirmOrderForm"
                                        style="font-size: 16px;"
                                    {% if page.cart.errors|length is same as(1) %}
                                        {% for error in page.cart.errors %}
                                            {% if _key != "promotion-not-found" %}
                                                disabled
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                    {% if page.cart.errors|length > 1 %}
                                        disabled
                                    {% endif %}
                                        type="submit">
                                    {{ "checkout.confirmSubmit"|trans|sw_sanitize }}
                                </button>

                                {% sw_include '@SwagPayPal/storefront/component/ecs-spb-checkout/ecs-spb-data.html.twig' %}
                            {% endblock %}
                        </form>
                    {% endblock %}
                </div>
            </div>
        {% endblock %}
    </div>
</div>
{% endblock %}

{% block page_checkout_confirm_tos_header %}{% endblock %}

{% block page_checkout_confirm_tos_control_checkbox %}
{#    <input type="checkbox"#}
{#           class="checkout-confirm-tos-checkbox custom-control-input{% if formViolations.getViolations('/tos') is not empty %} is-invalid{% endif %}"#}
{#           required="required"#}
{#           id="tos"#}
{#           form="confirmOrderForm"#}
{#           name="tos"/>#}
{% endblock %}


{% block page_checkout_confirm_tos_control_label %}
{#    <label for="tos"#}
{#           class="checkout-confirm-tos-label">#}
        {% block page_checkout_confirm_revocation_notice_link %}
                {{ "ersatzteilshop.page.agb"|trans|raw }}
                <br>
                <a href="{{ '/widerrufsrecht.html'|trans }}" target="_blank">
                    {{ 'ersatzteilshop.page.revocation'|trans }}
                </a>
        {% endblock %}
{#    </label>#}
{% endblock %}
