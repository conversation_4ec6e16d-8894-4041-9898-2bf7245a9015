<div class="card register-card">
    <div class="card-body">
        {% if cardTitle %}
            <h1 style="font-weight: 900;">Adresse</h1>
        {% endif %}

        <form action="{{ path('frontend.account.register.save') }}"
              id="checkoutRegisterForm"
              class="register-form"
              method="post"
              data-form-submit-loader="true"
              data-form-csrf-handler="true"
              data-form-validation="true"
              data-address-validate="true"
        >

            {{ sw_csrf('frontend.account.register.save') }}
            <input type="hidden" name="redirectTo" value="{{ redirectTo }}">
            <input type="hidden" name="redirectParameters" value="{{ redirectParameters }}">
            <input type="hidden" name="errorRoute" value="frontend.checkout.register.page"/>

            <div class="form-row">
                <!-- Linke Seite: Rechnungsadresse -->
                <div class="col-md-6">
                    <div class="register-personal register-billingaddress-container">

                        {% sw_include '@Storefront/storefront/page/checkout/address/address-personal.html.twig' with {
                            showBirthdayField: config('core.loginRegistration.showBirthdayField'),
                            data: address,
                            accounTypeRequired: true,
                            showVatIdField: true,
                            hideCompanyOptional: true
                        } %}

                        <div class="form-group">
                            {% block component_account_register_personal_mail %}
                                {% block component_account_register_personal_mail_input %}
                                    <input type="email"
                                           class="form-control{% if formViolations.getViolations('/email') is not empty %} is-invalid{% endif %}"
                                           autocomplete="section-personal email"
                                           id="personalMail"
                                           placeholder="{{ "account.personalMailPlaceholder"|trans|striptags }}"
                                           name="email"
                                           value="{{ data.get('email') }}"
                                        {% if config('core.loginRegistration.requireEmailConfirmation') %}
                                            data-form-validation-equal="personalMail"
                                        {% endif %}
                                           required="required">
                                {% endblock %}

                                {% block component_account_register_personal_mail_input_error %}
                                    {% if formViolations.getViolations('/email') is not empty %}
                                        {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' with {
                                            violationPath: '/email'
                                        } %}
                                    {% endif %}
                                {% endblock %}
                            {% endblock %}
                        </div>

                        {% sw_include '@Storefront/storefront/component/address/address-form.html.twig' with {
                            'prefix': 'billingAddress',
                            'showVatIdField': true,
                            'data': data.get('billingAddress'),
                            'hideCompanyOptional': true,
                            hideCompanyAddressFormRegister: true
                        } %}
                    </div>
                </div>

                <!-- Rechte Seite: Lieferadresse + Optionales Konto-Erstellen -->
                <div class="col-md-6 register-right-container" style="padding-left: 20px;">
                    <div class="register-address">
                        {% sw_include '@Storefront/storefront/page/checkout/address/shipping-address-option.html.twig' %}

                        <div class="register-shipping js-form-field-toggle-shipping-address d-none">
                            {% block component_account_register_address_shipping_header %}
                                <div class="card-title">
                                    {{ "account.registerAddressShippingHeader"|trans|sw_sanitize }}
                                </div>
                            {% endblock %}

                            {% block component_account_register_address_shipping_fields %}
                                {% block component_account_register_address_shipping_fields_personal %}
                                    {% sw_include '@Storefront/storefront/page/checkout/address/address-personal.html.twig' with {
                                        prefix: 'shippingAddress',
                                        data: data.get('shippingAddress'),
                                        accounTypeRequired: false,
                                        showVatIdField: false,
                                        hideCompanyOptional: false,
                                        hideCustomerTypeSelect: true
                                    } %}
                                {% endblock %}

                                {% block component_account_register_address_shipping_fields_address %}
                                    {% sw_include '@Storefront/storefront/component/address/address-form.html.twig' with {
                                        'prefix': 'shippingAddress',
                                        'data': data.get('shippingAddress'),
                                        'displayShippingCountry': true,
                                        hideCompanyAddressFormRegister: true
                                    } %}
                                {% endblock %}
                            {% endblock %}
                        </div>

                        <div class="register-shipping js-form-postfiliale-shipping-address d-none">
                            {% sw_include '@Storefront/storefront/page/checkout/address/postfiliale-shipping-address.html.twig' %}
                        </div>

                        <div class="register-shipping js-form-packstation-shipping-address d-none">
                            {% sw_include '@Storefront/storefront/page/checkout/address/packstation-shipping-address.html.twig' %}
                        </div>
                    </div>

                    <div class="register-account-optional-container">
                        <div class="card-title">
                            {{ 'checkout.createAccountOptional'|trans|sw_sanitize }}
                        </div>

                        <div class="register-option">
                            <div class="form-check">
                                <input class="form-check-input js-register-option"
                                       type="checkbox"
                                       name="createAccount"
                                       id="createAccountOption"
                                       value="1"
                                       {% if app.request.get('register') == '1' %}checked{% endif %}
                                >
                                <label class="form-check-label" for="createAccountOption">
                                    Daten für zukünftige Bestellungen speichern und Kundenkonto erstellen.
                                </label>
                            </div>

                            <div class="form-group js-register-form-group" {% if app.request.get('guest') == '1' %}style="display: none;"{% endif %}>
                                <input type="hidden" name="guest" value="{% if data.get('guest') %}1{% endif %}" id="personalGuest" disabled="disabled"/>
                                {% block component_account_register_personal_password %}
                                    <div class="password-wrapper">
                                        {% block component_account_register_personal_password_input %}
                                            <input type="password"
                                                   class="form-control{% if formViolations.getViolations('/password') is not empty %} is-invalid{% endif %} js-password-input"
                                                   autocomplete="new-password"
                                                   id="personalPassword"
                                                   placeholder="{{ 'account.personalPasswordPlaceholder'|trans|striptags }}"
                                                   name="password"
                                                   data-optional-validation="true"
                                                   minlength="8"
                                                   data-form-validation-equal="newPassword"
                                            >
                                        {% endblock %}

                                        {% block component_account_register_personal_password_input_error %}
                                            {% if formViolations.getViolations('/password') is not empty %}
                                                {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' with {
                                                    violationPath: '/password'
                                                } %}
                                            {% endif %}
                                        {% endblock %}
                                {% endblock %}
                                        <span class="toggle-password" data-toggle-password="personalPassword">
                                            <svg class="eye-icon eye-closed" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                                            <mask id="mask0_6639_17856" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                            <rect x="0.472656" width="24" height="24" fill="#D9D9D9"/>
                                            </mask>
                                            <g mask="url(#mask0_6639_17856)">
                                            <path d="M20.2727 22.6008L16.0727 18.4508C15.4893 18.6341 14.9018 18.7716 14.3102 18.8633C13.7185 18.955 13.106 19.0008 12.4727 19.0008C9.95599 19.0008 7.71432 18.3049 5.74766 16.9133C3.78099 15.5216 2.35599 13.7174 1.47266 11.5008C1.82266 10.6174 2.26432 9.79662 2.79766 9.03828C3.33099 8.27995 3.93932 7.60078 4.62266 7.00078L1.87266 4.20078L3.27266 2.80078L21.6727 21.2008L20.2727 22.6008ZM12.4727 16.0008C12.656 16.0008 12.8268 15.9924 12.9852 15.9758C13.1435 15.9591 13.3143 15.9258 13.4977 15.8758L8.09766 10.4758C8.04766 10.6591 8.01432 10.8299 7.99766 10.9883C7.98099 11.1466 7.97266 11.3174 7.97266 11.5008C7.97266 12.7508 8.41016 13.8133 9.28516 14.6883C10.1602 15.5633 11.2227 16.0008 12.4727 16.0008ZM19.7727 16.4508L16.5977 13.3008C16.7143 13.0174 16.806 12.7299 16.8727 12.4383C16.9393 12.1466 16.9727 11.8341 16.9727 11.5008C16.9727 10.2508 16.5352 9.18828 15.6602 8.31328C14.7852 7.43828 13.7227 7.00078 12.4727 7.00078C12.1393 7.00078 11.8268 7.03411 11.5352 7.10078C11.2435 7.16745 10.956 7.26745 10.6727 7.40078L8.12266 4.85078C8.80599 4.56745 9.50599 4.35495 10.2227 4.21328C10.9393 4.07161 11.6893 4.00078 12.4727 4.00078C14.9893 4.00078 17.231 4.69661 19.1977 6.08828C21.1643 7.47995 22.5893 9.28412 23.4727 11.5008C23.0893 12.4841 22.5852 13.3966 21.9602 14.2383C21.3352 15.0799 20.606 15.8174 19.7727 16.4508ZM15.1477 11.8508L12.1477 8.85078C12.6143 8.76745 13.0435 8.80495 13.4352 8.96328C13.8268 9.12161 14.1643 9.35078 14.4477 9.65078C14.731 9.95078 14.9352 10.2966 15.0602 10.6883C15.1852 11.0799 15.2143 11.4674 15.1477 11.8508Z" fill="#989898"/>
                                            </g>
                                            </svg>

                                            <svg class="eye-icon eye-open hidden" xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                            <mask id="mask0_6650_20749" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                                            <rect y="0.584961" width="24" height="24" fill="#D9D9D9"/>
                                            </mask>
                                            <g mask="url(#mask0_6650_20749)">
                                            <path d="M12 16.585C13.25 16.585 14.3125 16.1475 15.1875 15.2725C16.0625 14.3975 16.5 13.335 16.5 12.085C16.5 10.835 16.0625 9.77246 15.1875 8.89746C14.3125 8.02246 13.25 7.58496 12 7.58496C10.75 7.58496 9.6875 8.02246 8.8125 8.89746C7.9375 9.77246 7.5 10.835 7.5 12.085C7.5 13.335 7.9375 14.3975 8.8125 15.2725C9.6875 16.1475 10.75 16.585 12 16.585ZM12 14.785C11.25 14.785 10.6125 14.5225 10.0875 13.9975C9.5625 13.4725 9.3 12.835 9.3 12.085C9.3 11.335 9.5625 10.6975 10.0875 10.1725C10.6125 9.64746 11.25 9.38496 12 9.38496C12.75 9.38496 13.3875 9.64746 13.9125 10.1725C14.4375 10.6975 14.7 11.335 14.7 12.085C14.7 12.835 14.4375 13.4725 13.9125 13.9975C13.3875 14.5225 12.75 14.785 12 14.785ZM12 19.585C9.56667 19.585 7.35 18.9058 5.35 17.5475C3.35 16.1891 1.9 14.3683 1 12.085C1.9 9.80163 3.35 7.98079 5.35 6.62246C7.35 5.26413 9.56667 4.58496 12 4.58496C14.4333 4.58496 16.65 5.26413 18.65 6.62246C20.65 7.98079 22.1 9.80163 23 12.085C22.1 14.3683 20.65 16.1891 18.65 17.5475C16.65 18.9058 14.4333 19.585 12 19.585Z" fill="#1C1B1F"/>
                                            </g>
                                            </svg>
                                        </span>
                                    </div>
                                <small class="password-hint">muss mindestens 8 Zeichen enthalten</small>
                                {% block component_account_register_personal_password_confirmation %}
                                    <div class="password-wrapper">
                                        {% block component_account_register_personal_password_confirmation_input %}
                                            <input type="password"
                                                   class="form-control{% if formViolations.getViolations('/password') is not empty or formViolations.getViolations('/passwordConfirmation') is not empty %} is-invalid{% endif %} js-password-input"
                                                   autocomplete="new-password"
                                                   id="personalPasswordConfirmation"
                                                   placeholder="{{ 'account.personalPasswordConfirmationPlaceholder'|trans|striptags }}"
                                                   name="passwordConfirmation"
                                                   data-optional-validation="true"
                                                   data-form-validation-equal="newPassword">
                                        {% endblock %}
                                        {% block component_account_register_personal_password_confirmation_input_error %}
                                            {% if formViolations.getViolations('/password') is not empty %}
                                                {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' with {
                                                    violationPath: '/password'
                                                } %}
                                            {% endif %}

                                            {% if formViolations.getViolations('/passwordConfirmation') is not empty %}
                                                {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' with {
                                                    violationPath: '/passwordConfirmation'
                                                } %}
                                            {% endif %}
                                        {% endblock %}
                                {% endblock %}
                                        <span class="toggle-password" data-toggle-password="personalPasswordConfirmation">
                                            <svg class="eye-icon eye-closed" xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                                            <mask id="mask0_6639_17856" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                            <rect x="0.472656" width="24" height="24" fill="#D9D9D9"/>
                                            </mask>
                                            <g mask="url(#mask0_6639_17856)">
                                            <path d="M20.2727 22.6008L16.0727 18.4508C15.4893 18.6341 14.9018 18.7716 14.3102 18.8633C13.7185 18.955 13.106 19.0008 12.4727 19.0008C9.95599 19.0008 7.71432 18.3049 5.74766 16.9133C3.78099 15.5216 2.35599 13.7174 1.47266 11.5008C1.82266 10.6174 2.26432 9.79662 2.79766 9.03828C3.33099 8.27995 3.93932 7.60078 4.62266 7.00078L1.87266 4.20078L3.27266 2.80078L21.6727 21.2008L20.2727 22.6008ZM12.4727 16.0008C12.656 16.0008 12.8268 15.9924 12.9852 15.9758C13.1435 15.9591 13.3143 15.9258 13.4977 15.8758L8.09766 10.4758C8.04766 10.6591 8.01432 10.8299 7.99766 10.9883C7.98099 11.1466 7.97266 11.3174 7.97266 11.5008C7.97266 12.7508 8.41016 13.8133 9.28516 14.6883C10.1602 15.5633 11.2227 16.0008 12.4727 16.0008ZM19.7727 16.4508L16.5977 13.3008C16.7143 13.0174 16.806 12.7299 16.8727 12.4383C16.9393 12.1466 16.9727 11.8341 16.9727 11.5008C16.9727 10.2508 16.5352 9.18828 15.6602 8.31328C14.7852 7.43828 13.7227 7.00078 12.4727 7.00078C12.1393 7.00078 11.8268 7.03411 11.5352 7.10078C11.2435 7.16745 10.956 7.26745 10.6727 7.40078L8.12266 4.85078C8.80599 4.56745 9.50599 4.35495 10.2227 4.21328C10.9393 4.07161 11.6893 4.00078 12.4727 4.00078C14.9893 4.00078 17.231 4.69661 19.1977 6.08828C21.1643 7.47995 22.5893 9.28412 23.4727 11.5008C23.0893 12.4841 22.5852 13.3966 21.9602 14.2383C21.3352 15.0799 20.606 15.8174 19.7727 16.4508ZM15.1477 11.8508L12.1477 8.85078C12.6143 8.76745 13.0435 8.80495 13.4352 8.96328C13.8268 9.12161 14.1643 9.35078 14.4477 9.65078C14.731 9.95078 14.9352 10.2966 15.0602 10.6883C15.1852 11.0799 15.2143 11.4674 15.1477 11.8508Z" fill="#989898"/>
                                            </g>
                                            </svg>

                                            <svg class="eye-icon eye-open hidden" xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                            <mask id="mask0_6650_20749" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                                            <rect y="0.584961" width="24" height="24" fill="#D9D9D9"/>
                                            </mask>
                                            <g mask="url(#mask0_6650_20749)">
                                            <path d="M12 16.585C13.25 16.585 14.3125 16.1475 15.1875 15.2725C16.0625 14.3975 16.5 13.335 16.5 12.085C16.5 10.835 16.0625 9.77246 15.1875 8.89746C14.3125 8.02246 13.25 7.58496 12 7.58496C10.75 7.58496 9.6875 8.02246 8.8125 8.89746C7.9375 9.77246 7.5 10.835 7.5 12.085C7.5 13.335 7.9375 14.3975 8.8125 15.2725C9.6875 16.1475 10.75 16.585 12 16.585ZM12 14.785C11.25 14.785 10.6125 14.5225 10.0875 13.9975C9.5625 13.4725 9.3 12.835 9.3 12.085C9.3 11.335 9.5625 10.6975 10.0875 10.1725C10.6125 9.64746 11.25 9.38496 12 9.38496C12.75 9.38496 13.3875 9.64746 13.9125 10.1725C14.4375 10.6975 14.7 11.335 14.7 12.085C14.7 12.835 14.4375 13.4725 13.9125 13.9975C13.3875 14.5225 12.75 14.785 12 14.785ZM12 19.585C9.56667 19.585 7.35 18.9058 5.35 17.5475C3.35 16.1891 1.9 14.3683 1 12.085C1.9 9.80163 3.35 7.98079 5.35 6.62246C7.35 5.26413 9.56667 4.58496 12 4.58496C14.4333 4.58496 16.65 5.26413 18.65 6.62246C20.65 7.98079 22.1 9.80163 23 12.085C22.1 14.3683 20.65 16.1891 18.65 17.5475C16.65 18.9058 14.4333 19.585 12 19.585Z" fill="#1C1B1F"/>
                                            </g>
                                            </svg>
                                        </span>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% sw_include '@Storefront/storefront/component/captcha/base.html.twig' with { 'captchas': config('core.basicInformation.activeCaptchas') } %}

            <div class="register-submit">
                <a href="{{ path('frontend.checkout.cart.page') }}" class="btn button_fix checkout-register-back k11-font-size-16 hover-light-grey">
                    {{ "checkout.returnToCart"|trans|sw_sanitize }}
                </a>
                <button type="submit" class="btn button_fix checkout-register-submit k11-font-size-16 hover-light-green">
                    {{ "account.registerSubmit"|trans|sw_sanitize }}
                </button>
            </div>
        </form>
    </div>
</div>

{% sw_include '@Storefront/storefront/component/address/address-validate-modal.html.twig' %}

