{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}
{% set aswoProduct = page.product.extensions.XantenAswo %}
{% set faqs = page.extension('faq') %}

{% block layout_head_stylesheet %}
    {% if isHMRMode %}
        {# CSS will be loaded from the JS automatically #}
    {% else %}
        {% include '@Ersatzteilshop/storefront/layout/styles.html.twig' %}
    {% endif %}
{% endblock %}

{% block layout_head_meta_tags_general %}
    <meta name="author"
          content="{{ metaInformation.author|striptags }}"/>
    {% if aswoProduct %}
        <meta name="robots" content="noindex, follow"/>
    {% else %}
        <meta name="robots" content="index, follow"/>
    {% endif %}
    <meta name="revisit-after"
          content="{{ metaInformation.revisit|striptags }}"/>
    <meta name="keywords"
          content="{% block layout_head_meta_tags_keywords %}{{ metaKeywords }}{% endblock %}"/>
    <meta name="description"
          content="{% block layout_head_meta_tags_description %}{{ metaDescription }}{% endblock %}"/>
{% endblock %}

{% block layout_head_meta_tags_opengraph %}
    <meta property="og:type"
          content="product"/>
    <meta property="og:site_name"
          content="{{ config('core.basicInformation.shopName') }}"/>
    <meta property="og:url"
          content="{{ seoUrl('frontend.detail.page', { productId: page.product.id }) }}"/>
    <meta property="og:title"
          content="{{ metaTitle }}"/>

    <meta property="og:description"
          content="{{ metaDescription }}"/>
    <meta property="og:image"
          content="{{ page.product.cover.media.url }}"/>

    {% if page.product.manufacturer %}
        <meta property="product:brand"
              content="{{ page.product.manufacturer.translated.name }}"/>
    {% endif %}
    <meta property="product:price"
          content="{{ page.product.calculatedPrice.unitPrice|currency }}"/>
    <meta property="product:product_link"
          content="{{ seoUrl('frontend.detail.page', { productId: page.product.id }) }}"/>

    <meta name="twitter:card"
          content="product"/>
    <meta name="twitter:site"
          content="{{ config('core.basicInformation.shopName') }}"/>
    <meta name="twitter:title"
          content="{{ metaTitle }}"/>
    <meta name="twitter:description"
          content="{{ metaDescription }}"/>
    <meta name="twitter:image"
          content="{{ page.product.cover.media.url }}"/>
{% endblock %}

{% block layout_head_prev_next_links %}
    {% set currentPage = app.request.query.get('p', 1) %}
    {% if currentPage is empty or not(currentPage matches '/^\\d+$/') %}
        {% set currentPage = 1 %}
    {% endif %}
    {% set totalPage = (appliance.getCachedProductTotal() / 15)|round(0, 'ceil') %}
    {% set url = (app.request.attributes.get('sw-canonical-link') ?? (app.request.attributes.get('sw-sales-channel-absolute-base-url') ~ app.request.attributes.get('sw-original-request-uri')))|split('?') %}
    {% set url = url[0] %}

    {% if currentPage > 1 %}
        {% if currentPage == 2 %}
            <link rel="prev" href="{{ url }}">
        {% else %}
            <link rel="prev" href="{{ url ~ '?p=' ~ (currentPage - 1) }}">
        {% endif %}
    {% endif %}

    {% if currentPage < totalPage %}
        <link rel="next" href="{{ url ~ '?p=' ~ (currentPage + 1) }}">
    {% endif %}
{% endblock %}

{% block layout_head_meta_tags %}
    {{ parent() }}
    {% set cdnUrl = app.request.server.get('CDN_URL')~'/public/' %}
    {% set src = page.product.cover.media.url|replace({(cdnUrl): cloud_flare_image_url~ 'md/'}) %}
    <link rel="preload" href="{{ src }}" as="image">
{% endblock %}

{% block layout_head_title_inner %}
    {% set title %}{% apply spaceless %}
    {{ page.product.translated.name }} - {{ page.product.seoCategory.name }}{% if page.product.variation %} - {% for variation in page.product.variation %}{{ variation.group }}: {{ variation.option }}{% if page.product.variation|last != variation %}{{ " | " }}{% endif %}{% endfor %}{% endif %}
    {% endapply %}
    {% endset %}
    {% set title = title|u.truncate(70, '', false)  %}
    {% if title|length > 70 %}
        {% set title = title|u.beforeLast(' ')  %}
    {% endif %}
    {{ title }}
{% endblock %}

{% block layout_head_canonical %}
    {% if aswoProduct %}
        <link rel="canonical" href="{{ seoUrl('frontend.xanten-aswo.product.detail', { productId: page.product.id|lower|replace({"xantenaswo": "", "-": ""}) }) }}" />
    {% else %}
        <link rel="canonical" href="{{ seoUrl('frontend.detail.page', { productId: page.product.id }) }}" />
    {% endif %}
{% endblock %}
