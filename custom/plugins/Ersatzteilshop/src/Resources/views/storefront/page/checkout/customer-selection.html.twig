{% sw_extends '@Storefront/storefront/page/checkout/address/index.html.twig' %}

{% block base_header %}
    {% sw_include '@Storefront/storefront/layout/header/header-minimal-checkout.html.twig' %}
{% endblock %}

{% block base_main_container %}
    <div class="checkout-customer-selection k11-mb-60">
        <div class="d-grid k11-grid-template-1 k11-md-grid-template-3 k11-column-gap-20 k11-mb-40 k11-mt-40">
            {% block selection_wrapper_guest %}
                <div class="customer-selection-wrapper k11-mr-20">
                    <div class="customer-selection-wrapper-body">
                        <h4>Ohne Anmeldung fortfahren</h4>
                        <a href="{{ path('frontend.checkout.register.page', { 'guest': 1 }) }}">
                            <div class="customer-selection-button hover-light-green">
                                <span>Als Gast bestellen</span>
                            </div>
                        </a>
                    </div>
                </div>
            {% endblock %}
            {% block selection_wrapper_create_account %}
                <div class="customer-selection-wrapper k11-mr-20">
                    <div class="customer-selection-wrapper-body">
                        <h4>Kundenkonto erstellen</h4>
                        <p><svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                                <mask id="mask0_6351_14489" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                    <rect x="0.507812" width="24" height="24" fill="#D9D9D9"/>
                                </mask>
                                <g mask="url(#mask0_6351_14489)">
                                    <path d="M10.5078 16.4L6.50781 12.4L7.90781 11L10.5078 13.6L17.1078 7L18.5078 8.4L10.5078 16.4Z" fill="#202E3D"/>
                                </g>
                                </svg>Schneller bestellen</p>
                        <p><svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                                <mask id="mask0_6351_14489" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                    <rect x="0.507812" width="24" height="24" fill="#D9D9D9"/>
                                </mask>
                                <g mask="url(#mask0_6351_14489)">
                                    <path d="M10.5078 16.4L6.50781 12.4L7.90781 11L10.5078 13.6L17.1078 7L18.5078 8.4L10.5078 16.4Z" fill="#202E3D"/>
                                </g>
                                </svg>Bestellübersicht</p>
                        <p><svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                                <mask id="mask0_6351_14489" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                                    <rect x="0.507812" width="24" height="24" fill="#D9D9D9"/>
                                </mask>
                                <g mask="url(#mask0_6351_14489)">
                                    <path d="M10.5078 16.4L6.50781 12.4L7.90781 11L10.5078 13.6L17.1078 7L18.5078 8.4L10.5078 16.4Z" fill="#202E3D"/>
                                </g>
                                </svg>Status und Paketverfolgung</p>

                        <a href="{{ path('frontend.checkout.register.page', { 'register': 1 }) }}">
                            <div class="customer-selection-button hover-light-green">
                                <span>Registrieren</span>
                            </div>
                        </a>
                    </div>
                </div>
            {% endblock %}
            {% block selection_wrapper_login %}
                <div class="customer-selection-wrapper">
                    <div class="customer-selection-wrapper-body">
                        <h4>Ich habe bereits ein Kundenkonto</h4>
                        <form action="{{ path('frontend.account.login') }}"
                              method="post"
                              id="loginForm"
                              data-form-csrf-handler="true"
                              data-form-validation="true">

                            {# CSRF-Token einfügen #}
                            {{ sw_csrf('frontend.account.login') }}

                            {# Hidden-Felder für Weiterleitung – setze hier ggf. Standardwerte oder Variablen #}
                            <input type="hidden" name="redirectTo" value="{{ redirectTo|default('frontend.checkout.confirm.page') }}">
                            <input type="hidden" name="redirectParameters" value="{{ redirectParameters|default('') }}">

                            <div class="form-group">
                                <input type="email"
                                       name="email"
                                       placeholder="E-Mail"
                                       class="form-control"
                                       required>
                            </div>
                            <div class="form-group k11-mb-0">
                                <input type="password"
                                       name="password"
                                       placeholder="Passwort"
                                       class="form-control"
                                       required>
                                <a href="{{ seoUrl('frontend.account.recover.page') }}" class="customer-selection-password-link">
                                    <span>Passwort vergessen?</span>
                                </a>
                            </div>
                            <button type="submit" class="btn hover-light-green">
                                <span>Einloggen</span>
                            </button>
                        </form>
                    </div>
                </div>
            {% endblock %}
            <ul class="customer-selection-button-mobile-warenkorb d-none">
                <li>
                    <a class="tab-head" href="/checkout/cart">
                        <span>Zurück zum Warenkorb</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
{% endblock %}
