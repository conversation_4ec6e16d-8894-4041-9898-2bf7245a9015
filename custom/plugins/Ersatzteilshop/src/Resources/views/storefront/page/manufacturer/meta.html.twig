{% sw_extends '@Storefront/storefront/layout/meta.html.twig' %}

{% block layout_head_meta_tags %}
    {% set url = (app.request.attributes.get('sw-canonical-link') ?? (app.request.attributes.get('sw-sales-channel-absolute-base-url') ~ app.request.attributes.get('sw-original-request-uri')))|split('?')|first %}
    {% set headlineContent = page.manufacturer.translated.customFields.seo_page_title ?? 'ersatzteilshop.page.brandHeading'|trans({'%brand%': page.manufacturer.translated.name}) %}
        <meta itemprop="name" content="{{ page.manufacturer.translated.name }}">
        <meta itemprop="headline" content="{{ headlineContent }}">
        <meta itemprop="description" content="{{ page.manufacturer.translated.description }}">
        <meta itemprop="url" content="{{ url }}">
    {{ parent() }}
    {% sw_include '@Storefront/storefront/component/meta.html.twig' %}
{% endblock %}
