.content-main-video {
  overflow-x: auto !important;
}

html {
  scroll-behavior: smooth;
}

.video-header {
  padding-top: 1rem;
  display: flex;
  flex-direction: column-reverse;
  gap: 0.5rem;
  margin-bottom: 1rem;

  h1 {
    margin: 0;
  }

  .go-back-link {
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  @media (min-width: 768px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    h1 {
      order: 1;
    }

    .go-back-link {
      order: 2;
    }
  }
}

.video-library-bg {
  background: #202E3D;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100px;
  justify-content: center;
  align-items: center;

  h1 {
    color: #FFF;
    text-align: center;
    margin-bottom: 0;
    line-height: 1;
    font-size: 30px;
  }
}

.container-main.video-library {
  padding-top: 60px;
  @include media-breakpoint-down(sm) {
    padding-top: 0;
    margin-top: 40px;
  }
  .table {
    margin-bottom: 0;
    h2 {
      margin-bottom: 0;
    }
    .mobile-toggle {
      border-radius: 8px;
      padding: 9px 10px;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      margin-bottom: 20px;
      -ms-flex-pack: justify;
      justify-content: space-between;
      border: 2px solid #e0e0e0;
      font-weight: 400;
      @include media-breakpoint-down(sm) {
        margin-top: 0;
      }
    }

    .mobile-toggle .toggle-icon {
      transition: transform 0.3s ease-in-out;
      transform: rotate(90deg);
    }
    .mobile-toggle.collapsed .toggle-icon {
      transform: rotate(0deg);
    }
    .mobile-toggle:not(.collapsed) {
      border: 2px solid #202e3d;
      font-weight: 700;
    }
    .video-library_table_paragraph {
      font-size: 16px;
      font-style: normal;
      font-weight: 900;
      line-height: 100%;
    }
    .result-list {
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      justify-content: left;
      list-style: none;
      padding-left: 0;
      margin-bottom: 40px;
      white-space: nowrap;
      -ms-overflow-style: none;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      gap: 10px;
      overflow-wrap: break-word;
    }
  }
}

.filter-selected {
  display: inline;
  background-color: #FFF;
  border: 2px solid #202E3D;
  height: 28px;
  padding: 0;
  border-radius: 10px;
  &:hover {
    cursor: pointer;
  }
  .link-selected {
    color: #222f3e;
    display: block;
    width: 100%;
    text-align: center;
    min-width: 64px;
    padding-top: 2px;
    padding-left: 5px;
    padding-right: 5px;
    font-weight: 900;
    position: relative;
    top: -1px;
  }
}

.filter-deselected {
  display: inline;
  background-color: #FFF;
  border: 2px solid #D8D8D8;
  height: 28px;
  padding: 0;
  border-radius: 10px;
  @media (min-width: 768px) {
    &:hover {
      cursor: pointer;
      border: 2px solid #202E3D;
    }
  }
  .link-deselected {
    color: #222f3e;
    display: block;
    width: 100%;
    text-align: center;
    min-width: 64px;
    padding-top: 2px;
    padding-left: 8px;
    padding-right: 8px;
  }
}

.yt-thumbnail-placeholder {
  visibility: visible;
  opacity: 1;
}

.ytp-gradient-top {
  height: 48px;
  top: 0;
  z-index: 25;
  width: 100%;
  position: absolute;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5) 0%, transparent 100%);
  pointer-events: none;
}

.load-more-videos {
  display: inline;
  background-color: #FFF;
  border: 2px solid #D8D8D8;
  border-radius: 10px;
  color: #222f3e;
  height: 28px;
  text-align: center;
  min-width: 64px;
  padding: 0 8px;
  &:hover {
    cursor: pointer;
    display: inline;
    background-color: #FFF;
    border: 2px solid #202E3D;
    border-radius: 10px;
    padding: 0 5px;
    font-weight: 900;
  }
  &:focus {
      background-color: #FFF;
      border: 2px solid #202E3D;
      border-radius: 10px;
      padding: 0 5px;
      font-weight: 900;
      box-shadow: unset !important;
      outline: none;
  }
}

.video-heading {
  font-size: 16px;
  font-style: normal;
  font-weight: 900;
  line-height: 100%;
  margin-top: 40px;
  @include media-breakpoint-down(sm) {
    margin-top: 20px;
  }
}

.video-paragraph {
  margin-top: 10px;
  margin-bottom: 20px;
}
.chapter-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: #f1f1f1;
  padding: 10px;
  height: 250px;
  margin-top: 20px;
  overflow: hidden;
}

.chapter-scroll-area {
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
}

.chapter {
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
  cursor: pointer;
}

.chapter-title-text {
  font-size: 14px;
  text-align: left;
  max-width: 150px;
  font-weight: 500;
  overflow-wrap: break-word;
  white-space: normal;
}

.chapter-thumbnail {
  width: 145px;
  height: 90px;
  border-radius: 8px;
}

.chapter-time {
  position: relative;
  bottom: 25px;
  right: -90px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  width: 45px;
  border-radius: 5px;
  padding-left: 10px;
  padding-right: 10px;
}

.chapter-content {
  display: inline-block;
  width: 165px;
  padding: 10px;
  height: 175px;
  border-radius: 8px;
  background-color: #fff;
}

.chapter-content:hover {
  background-color: #cfcfcf;
}

.chapter.active .chapter-content {
  background-color: #cfcfcf;
}

.videoTag {
  font-size: small;
  font-weight: bold;
  background-color: #f1f1f1;
  border-radius: 10px;
  padding: 6px 10px;
  margin: 4px;
  display: inline-block;
}

.youtube_thumbnail {
  position: absolute;
  height: 551px;
  object-fit: cover;
  z-index: 5;
  display: block;
  width: 100%
}

.youtube_thumbnail_overview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.playbutton {
  width: 66px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.5;
}

.no-results-info-container {
  gap: 20px;
  margin-bottom: 20px;
  .no-results-info-container_information{
    display: flex;
    width: 375px;
    height: 225px;
    padding: 16px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    border-radius: 8px;
    border: 2px solid #E0E0E0;
    background: #FFF;
    @include media-breakpoint-down(sm) {
      width: 100%;
      margin-top: 20px;
    }
  }
}

.no-results-video-container {
  display: grid;
  grid-template-columns: repeat(auto-fill,minmax(280px,1fr));
  column-gap: 40px;
  row-gap: 10px;
  padding: 0;
  margin: 0;
}



@include media-breakpoint-down(sm) {
  .chapter-container {
    display: block;
  }
  .video-library-bg {
    height: 150px;
  }
  .youtube_thumbnail {
    position: absolute;
    height: 215px;
    object-fit: cover;
    z-index: 5;
    display: block;
    width: 100%
  }
}

@keyframes scale-out {
  to {
    scale: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    scale: 0;
  }

  to {
    opacity: 1;
    scale: 1;
  }
}

::view-transition-group(*) {
  animation-duration: 0.35s;
}

::view-transition-new(*):only-child {
  animation-name: fade-in;
}

::view-transition-old(*):only-child {
  animation-name: scale-out;
}

#video-comments {
  padding-top: 0 !important;
}

.video-library-comment-form {
  .comment-form-input,
  .comment-form-textarea {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px;
    font-size: 14px;
    height: 42px;
    
    &:focus {
      border-color: #202E3D;
      box-shadow: none;
      outline: none;
    }
    
    &::placeholder {
      color: #989898;
      font-weight: 400;
    }
  }
  
  .comment-form-textarea {
    resize: vertical;
    height: 126px;
    min-height: 126px;
  }
  
  .comment-form-name-email-row {
    margin-bottom: 20px;
    
    .comment-form-name-col {
      padding-right: 10px;
    }
    
    .comment-form-email-col {
      padding-left: 10px;
    }
  }
  
  .comment-form-subject-row {
    margin-bottom: 20px;
  }
  
  .comment-form-textarea-row {
    margin-bottom: 20px;
  }
  
  @media (max-width: 767.98px) {
    .comment-form-name-email-row {
      margin-bottom: 10px;
      
      .comment-form-name-col {
        margin-bottom: 10px;
      }
    }
    
    .comment-form-subject-row {
      margin-bottom: 10px;
    }
    
    .comment-form-textarea-row {
      margin-bottom: 10px;
    }
  }
  
  .comment-form-buttons {
    .comment-form-btn-cancel,
    .comment-form-btn-submit {
      height: 42px;
      border-radius: 8px;
      padding: 9px 0;
      font-weight: 700;
      font-size: 16px;
      border: none;
      
      &:focus {
        box-shadow: none;
        outline: none;
      }
    }
    
    .comment-form-btn-cancel {
      background-color: #e0e0e0;
      color: #202E3D;
      
      &:hover {
        background-color: #d0d0d0;
        color: #202E3D;
      }
    }
    
    .comment-form-btn-submit {
      background-color: #74cb7b;
      color: #202E3D;
      
      &:hover {
        background-color: #65b86b;
        color: #202E3D;
      }
    }
  }
}

.video-library-comment-form-wrapper {
  @media (max-width: 767.98px) {
    padding: 10px !important;
  }
}
.comment-item {
  border-bottom: 1px solid #E0E0E0;
  padding-bottom: 20px;
  margin-bottom: 25px;

  .comment-header {
    margin-bottom: 15px;
  }
  
  .comment-author {
    font-size: 14px;
    font-weight: 400;
    color: #202E3D;
  }
  
  .comment-date {
    font-size: 14px;
    color: #202E3D;
    font-weight: 400;
  }
  
  .comment-subject {
    font-size: 14px;
    font-weight: 700;
    color: #202E3D;
    margin-top: 5px;
  }
  
  .comment-content {
    font-size: 14px;
    line-height: 1.5;
    color: #202E3D;
  }
  
  @media (max-width: 767.98px) {
    margin-bottom: 40px;
    
    .comment-header {
      margin-bottom: 20px;
    }
    
    .comment-subject {
      margin-top: 20px;
    }
  }
}
.comment-success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background-color: #f8f9fa;
  border: 2px solid #74CB7B;
  border-radius: 8px;
  padding: 16px 20px;
  margin: 20px 0;
  
  .success-text {
    font-size: 16px;
    color: #202E3D;
    font-weight: 500;
  }
  
  .success-icon {
    width: 20px;
    height: 20px;
    background-color: #74CB7B;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    
    &::after {
      content: '✓';
      color: white;
      font-size: 12px;
      font-weight: bold;
      line-height: 1;
    }
  }
}

.video-library-interactions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
  padding: 15px 0;
  width: 100%;

  @media (max-width: 767.98px) {
    gap: 15px;
    margin: 15px 0;
    padding: 12px 0;
  }
}

.interaction-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #202E3D;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  
  &:hover:not(.processing) {
    text-shadow: 0px 0px 0.8px #202E3D;
  }
  
  &.processing {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &:focus {
    outline: none;
  }
  
  @media (max-width: 767.98px) {
    padding: 6px 10px;
    font-size: 13px;
    gap: 6px;
  }
}

.interaction-icon {
  flex-shrink: 0;
}

.interaction-icon-heart {
  fill: white;
  stroke: #202E3D;
  stroke-width: 1;
}

.interaction-btn-like.liked .interaction-icon-heart {
  fill: #202E3D;
  stroke: #202E3D;
}

.interaction-count {
  min-width: 20px;
  text-align: center;
  font-weight: 600;
}


.share-feedback {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #74CB7B;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  
  &.show {
    transform: translateX(0);
  }
  
  @media (max-width: 767.98px) {
    top: 10px;
    right: 10px;
    padding: 10px 16px;
    font-size: 14px;
  }
}

.video-request-banner {
  display: flex;
  padding: 20px;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex: 1 0 0;
  border-radius: 8px;
  border: 1px solid #E0E0E0;
  background-color: #FFFFFF;
  margin: 20px 0;
  transition: all 0.3s ease;
  
  &.video-request-banner--hidden {
    display: none;
  }
  
  &.video-request-banner--success {
    .video-request-btn {
      display: none;
    }
    
    .video-request-text {
      text-align: center;
      width: 100%;
    }
  }
  
  @media (max-width: 767.98px) {
    flex-direction: column;
    padding: 16px;
    margin: 16px 0;
  }
}

.video-request-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  width: 100%;
  
  @media (max-width: 767.98px) {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

.video-request-text {
  color: #000;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  flex: 1 1 auto;
  
  @media (max-width: 767.98px) {
    text-align: center;
    flex: none;
  }
}

.video-request-btn {
  display: flex;
  height: 42px;
  padding: 8px 40px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex: 0 0 auto;
  background-color: #74CB7B;
  border: none;
  border-radius: 8px;
  color: #202E3D;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  
  &:hover {
    background-color: #65b86b;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(116, 203, 123, 0.3);
  }
  
  &.processing {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  @media (max-width: 767.98px) {
    width: 100%;
    flex: none;
  }
}

.video-request-icon {
  flex-shrink: 0;
  width: 25px;
  height: 24px;
  
  @media (max-width: 767.98px) {
    width: 20px;
    height: 19px;
  }
}

.video-request-btn-text {
  font-weight: 700;
  font-size: 16px;
  color: #202E3D;
  
  @media (max-width: 767.98px) {
    font-size: 14px;
  }
}

// Comment pagination styles
.comment-pagination {
  margin-top: 20px;
  padding: 20px 0;
}

.comment-pagination-info {
  text-align: center;
  margin-bottom: 15px;
  color: #666;
}

.comment-pagination-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.comment-pagination-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .icon {
    width: 16px;
    height: 16px;
  }
}

.comments-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.video-library-comments-list {
  position: relative;
  min-height: 200px;

  &.is-loading .comments-list-container {
    opacity: 0.5;
  }
}

.howto-accordion {
  .howto-accordion-item {
    background-color: #FFFFFF;
    
    &:last-of-type:not(.howto-step-accordion) {
      .howto-accordion-header {
        border-bottom: 1px solid #E0E0E0;
      }
    }
  }
  
  .howto-accordion-header {
    cursor: pointer;
    border-bottom: 1px solid #E0E0E0;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #f8f9fa;
    }
  }
  
  .howto-accordion-arrow {
    transition: transform 0.3s ease;
    flex-shrink: 0;
  }
  
  .howto-accordion-content {
    display: none;
    overflow: hidden;
    background-color: #FFFFFF;
  }
  
  .howto-show-all {
    border-top: 1px solid #E0E0E0;
  }
  
  .howto-show-all-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #202E3D;
    transition: all 0.2s ease;
    
    &:hover {
      text-shadow: 0px 0px 0.8px #202E3D;
    }
    
    &:focus {
      outline: none;
    }
  }
  
  .howto-tools-list,
  .howto-supplies-list {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
    
    li {
      position: relative;
      padding-left: 20px;
      
      &:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #202E3D;
        font-weight: bold;
      }
    }
  }
  
  .howto-step-name {
    margin-bottom: 0;
  }
  
  .howto-step-text {
    margin-bottom: 0;
  }
  
  .howto-step-hidden {
    display: none;
  }
}
