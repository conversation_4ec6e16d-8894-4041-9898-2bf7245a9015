/*
Payment method
==============================================
Custom styling for payment form used e.g. on account and checkout pages.
*/

.payment-method {
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid #D8D8D8;
    padding: 20px 20px 15px 20px;

    @media (max-width: 768px) {
        padding: 10px 10px 5px 10px;
    }

    .payment-method-image {
        float: none;
    }

    .payment-method-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 5px;
    }

    .payment-method-name {
        flex-grow: 1;
    }

    .toggle-icon {
        display: block;
    }
}

body.is-ctl-checkout {
    .custom-control {
        padding-left: 0 !important;
    }
}

.custom-control {
    .custom-payment-control-label {
        position: relative;
        padding-left: 40px;
        cursor: pointer;

        // äußere Hülle des Radio Buttons
        &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0; // <- statt 50%
            transform: none; // <- statt translateY(-50%)
            width: 28px !important;
            height: 28px !important;
            background-color: #fff !important;
            border: 2px solid #ccc !important;
            border-radius: 50% !important;
            box-sizing: border-box;
        }

        &::after {
            content: "";
            position: absolute;
            left: 7px;
            top: 7px;
            width: 14px;
            height: 14px;
            background-color: transparent;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
    }

    // wenn :checked → innerer Punkt sichtbar
    .custom-control-input:checked ~ .custom-payment-control-label::after {
        background-color: #202E3D;
        background-image: none;
    }

    // Wichtig: ::before NICHT einfärben beim Checked-Zustand!
    .custom-control-input:checked ~ .custom-payment-control-label::before {
        background-color: #fff !important;
        //border-color: #ccc !important;
    }

    .custom-control-input:focus ~ .custom-control-label::before {
        box-shadow: none;
    }
}


