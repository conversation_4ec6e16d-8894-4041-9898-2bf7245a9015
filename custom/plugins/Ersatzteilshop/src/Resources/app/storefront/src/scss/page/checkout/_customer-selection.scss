.checkout-customer-selection h1 {
    margin-top: 40px;
}

.customer-selection-wrapper {
    width: 100%;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #E0E0E0;
    margin-top: 20px;
    align-self: flex-start;

    @media (max-width: 768px) {
        padding: 10px;
        margin-bottom: 20px;
    }
}

.customer-selection-wrapper button {
    width: 100%;
    display: flex;
    height: 42px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 8px;
    background: #74CB7B;
    border: none;
    margin-top: 20px;

    @media (max-width: 768px) {
        margin-top: 10px;
    }
}

.customer-selection-wrapper button span {
    color: #202E3D;
    font-size: 16px;
    font-weight: 900;
}

.customer-selection-button {
    width: 100%;
    display: flex;
    height: 42px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 8px;
    background: #74CB7B;
    border: none;
    margin-top: 20px;

    @media (max-width: 768px) {
        margin-top: 10px;
    }
}

.customer-selection-button span {
    color: #202E3D;
    font-size: 16px;
    font-weight: 900;
}

.customer-selection-wrapper h4 {
    font-size: 14px;
    font-style: normal;
    font-weight: 900;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    @media (max-width: 768px) {
        margin-bottom: 10px;
    }
}

.customer-selection-wrapper p {
    margin: 0;
}

.customer-selection-wrapper p svg {
    margin-right: 10px;
}

.customer-selection-wrapper input {
    border-radius: 8px;
    border: 2px solid var(--dunkel-grau, #E0E0E0);
    display: flex;
    height: 42px;
    padding: 10px;
    align-items: center;
    gap: 10px;
}

.customer-selection-password-link {
    color: var(--Hover, #202E3D) !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 21px */
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;

    &:hover {
        text-decoration: underline !important;
    }
}

.customer-selection-password-link span {
    margin-top: 10px;
    text-align: right;
    display: grid;
}

.customer-selection-button-mobile-warenkorb {
    @include media-breakpoint-down(sm) {
        display: block !important;
    }

    margin-top: 20px;
    list-style: none;
    padding: 0;
    flex-wrap: wrap;
    gap: 20px;

    .tab-head {
        border-radius: 8px;
        border: 2px solid #E0E0E0;
        background: #E0E0E0;
        padding: 9px 0px;
        font-size: 20px;
        display: flex;
        height: 42px;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: inherit;
        cursor: pointer;

        @include media-breakpoint-down(sm) {
            width: 100%;
        }
        span {
            font-weight: 900;
            line-height: normal;
            font-size: 16px;
            color: #202E3D;
        }
    }
}

.k11-logo-mobile-center {

    img {
        margin-left: 10px;
    }

    @media (max-width: 768px) {
        text-align: center;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 20px;

        img {
            margin-left: 0px;
        }
    }
    @media (min-width: 992px) {
        body.is-ctl-checkout.is-act-confirmpage & {
            margin-left: 16.6666666667%;
        }
    }
}





