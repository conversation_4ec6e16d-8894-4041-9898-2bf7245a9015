import Plugin from 'src/plugin-system/plugin.class';

export default class VideoLibraryInteractionPlugin extends Plugin {
    static options = {
        shareSelector: '[data-interaction-share]',
        likeSelector: '[data-interaction-like]',
        commentSelector: '[data-interaction-comment]',
        commentPanelSelector: '.sw-cms-el-toggles .panel',
        commentFormSelector: '[data-video-comment-form]',
        likeStorageKeyPrefix: 'video_library_liked'
    };

    init() {
        this.videoId = this.el.dataset.videoId;
        this.likeRouteUrl = `/video-library/${this.videoId}/like`;
        this.shareRouteUrl = `/video-library/${this.videoId}/share`;
        this.requestRouteUrl = `/video-library/${this.videoId}/request`;
        this.shareSuccessMsg = this.el.dataset.shareSuccessMsg || 'Link kopiert!';
        this.shareErrorMsg = this.el.dataset.shareErrorMsg || '<PERSON><PERSON> beim <PERSON>n';
        
        this.isLikeProcessing = false;
        this.isShareProcessing = false;
        this.isRequestProcessing = false;
        
        this._registerEvents();
        this._initializeLikeState();
        
        this._initializeVideoRequestBanner();
    }

    static get pluginName() {
        return 'VideoLibraryInteractionPlugin';
    }

    _registerEvents() {
        const shareBtn = this.el.querySelector(this.options.shareSelector);
        const likeBtn = this.el.querySelector(this.options.likeSelector);
        const commentBtn = this.el.querySelector(this.options.commentSelector);

        if (shareBtn) {
            shareBtn.addEventListener('click', this._onShareClick.bind(this));
        }

        if (likeBtn) {
            likeBtn.addEventListener('click', this._onLikeClick.bind(this));
        }

        if (commentBtn) {
            commentBtn.addEventListener('click', this._onCommentClick.bind(this));
        }
    }

    _initializeLikeState() {
        const isLiked = this._getLikedState();
        const likeBtn = this.el.querySelector(this.options.likeSelector);
        
        if (likeBtn && isLiked) {
            likeBtn.classList.add('liked');
        }
    }

    async _onShareClick(event) {
        event.preventDefault();
        
        const shareBtn = event.currentTarget;
        
        if (this.isShareProcessing) {
            return;
        }
        
        this.isShareProcessing = true;
        shareBtn.classList.add('processing');
        
        const videoTitle = document.title;
        const videoUrl = window.location.href;
        const metaDescription = document.querySelector('meta[name="description"]');
        const videoDescription = metaDescription ? metaDescription.content : '';

        if (navigator.share && navigator.canShare && navigator.canShare({
            title: videoTitle,
            text: videoDescription,
            url: videoUrl
        })) {
            try {
                await navigator.share({
                    title: videoTitle,
                    text: videoDescription,
                    url: videoUrl
                });
                this._incrementShareCount();
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.log('Web Share API failed, falling back to clipboard:', error);
                    this._fallbackShare(videoUrl);
                }
            }
        } else {
            console.log('Web Share API not available, using clipboard fallback');
            this._fallbackShare(videoUrl);
        }
        
        setTimeout(() => {
            this.isShareProcessing = false;
            shareBtn.classList.remove('processing');
        }, 500);
    }

    async _fallbackShare(url) {
        try {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                await navigator.clipboard.writeText(url);
                this._showShareFeedback(this.shareSuccessMsg);
                this._incrementShareCount();
            } else {
                this._legacyFallbackShare(url);
            }
        } catch (error) {
            console.error('Clipboard error:', error);
            this._legacyFallbackShare(url);
        }
    }

    _legacyFallbackShare(url) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = url;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);
            
            if (successful) {
                this._showShareFeedback(this.shareSuccessMsg);
                this._incrementShareCount();
            } else {
                this._showShareFeedback(this.shareErrorMsg, true);
            }
        } catch (error) {
            console.error('Legacy copy error:', error);
            this._showShareFeedback(this.shareErrorMsg, true);
        }
    }

    _showShareFeedback(message, isError = false) {
        const feedback = document.createElement('div');
        feedback.className = `share-feedback ${isError ? 'error' : ''}`;
        feedback.textContent = message;
        
        document.body.appendChild(feedback);
        
        setTimeout(() => feedback.classList.add('show'), 100);
        
        setTimeout(() => {
            feedback.classList.remove('show');
            setTimeout(() => feedback.remove(), 300);
        }, 2000);
    }

    async _incrementShareCount() {
        try {
            const response = await fetch(this.shareRouteUrl, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Share response data:', data);
                if (data.success) {
                    this._updateShareCount(data.shares);
                } else {
                    console.error('Share request failed - success=false:', data);
                }
            } else {
                const errorText = await response.text();
                console.error(errorText);

            }
        } catch (error) {
            console.error('Network error during share request:', error);
        }
    }

    _updateShareCount(newCount) {
        const shareBtn = this.el.querySelector(this.options.shareSelector);
        const countElement = shareBtn && shareBtn.querySelector('.interaction-count');
        
        if (countElement) {
            countElement.textContent = newCount;
        }
    }

    async _onLikeClick(event) {
        event.preventDefault();
        
        const likeBtn = event.currentTarget;
        
        if (this.isLikeProcessing) {
            return;
        }
        
        this.isLikeProcessing = true;
        likeBtn.classList.add('processing');
        const wasLiked = likeBtn.classList.contains('liked');
        const newLikedState = !wasLiked;
        
        likeBtn.classList.toggle('liked', newLikedState);
        this._setLikedState(newLikedState);
        
        const countElement = likeBtn.querySelector('.interaction-count');
        const currentCount = parseInt(countElement.textContent) || 0;
        countElement.textContent = newLikedState ? currentCount + 1 : Math.max(0, currentCount - 1);

        try {
            const formData = new FormData();
            formData.append('liked', newLikedState ? '1' : '0');

            const response = await fetch(this.likeRouteUrl, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    countElement.textContent = data.likes;
                } else {
                    this._revertLikeState(likeBtn, wasLiked, currentCount);
                }
            } else {
                const errorText = await response.text();
                this._revertLikeState(likeBtn, wasLiked, currentCount);
            }
        } catch (error) {
            this._revertLikeState(likeBtn, wasLiked, currentCount);
        } finally {
            setTimeout(() => {
                this.isLikeProcessing = false;
                likeBtn.classList.remove('processing');
            }, 500);
        }
    }

    _revertLikeState(likeBtn, wasLiked, originalCount) {
        likeBtn.classList.toggle('liked', wasLiked);
        this._setLikedState(wasLiked);
        
        const countElement = likeBtn.querySelector('.interaction-count');
        countElement.textContent = originalCount;
    }

    _onCommentClick(event) {
        event.preventDefault();
        
        const commentForm = document.querySelector(this.options.commentFormSelector);
        const commentPanel = commentForm && commentForm.closest('.panel');
        
        if (commentPanel && commentPanel.classList.contains('hide')) {
            commentPanel.classList.remove('hide');
        }
        
        setTimeout(() => {
            if (commentPanel) {
                try {
                    commentPanel.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'start' 
                    });
                } catch (e) {
                    commentPanel.scrollIntoView();
                }
            }
            
            if (commentForm) {
                const firstInput = commentForm.querySelector('input[type="text"], textarea');
                if (firstInput) {
                    firstInput.focus();
                }
            }
        }, 300);
    }

    _getLikedState() {
        const key = `${this.options.likeStorageKeyPrefix}_${this.videoId}`;
        return localStorage.getItem(key) === 'true';
    }

    _setLikedState(isLiked) {
        const key = `${this.options.likeStorageKeyPrefix}_${this.videoId}`;
        if (isLiked) {
            localStorage.setItem(key, 'true');
        } else {
            localStorage.removeItem(key);
        }
    }

    _initializeVideoRequestBanner() {
        const banner = document.querySelector('[data-video-request-banner]');
        if (!banner) {
            return;
        }

        const videoId = banner.dataset.videoId;
        const pageUrl = banner.dataset.pageUrl;

        this.requestRouteUrl = `/video-library/${videoId}/request`;

        const storageKey = `video_request_hidden_${videoId}`;
        const storedData = localStorage.getItem(storageKey);
        
        if (storedData) {
            try {
                const requestData = JSON.parse(storedData);
                const currentTime = Date.now();
                const expiryTime = requestData.timestamp + (24 * 60 * 60 * 1000);

                if (currentTime < expiryTime && requestData.videoId === videoId) {
                    banner.classList.remove('video-request-banner--hidden');
                    this._showSuccessState(banner);
                    return;
                } else {
                    localStorage.removeItem(storageKey);
                }
            } catch (error) {
                localStorage.removeItem(storageKey);
            }
        }

        banner.classList.remove('video-request-banner--hidden');
        
        const requestBtn = banner.querySelector('[data-video-request-btn]');

        if (requestBtn) {
            requestBtn.addEventListener('click', () => this._onVideoRequestClick(banner, videoId));
        }
    }

    async _onVideoRequestClick(banner, videoId) {
        if (this.isRequestProcessing) return;

        this.isRequestProcessing = true;
        const btn = banner.querySelector('[data-video-request-btn]');
        btn.classList.add('processing');

        try {
            const response = await fetch(this.requestRouteUrl, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this._storeVideoRequestState(videoId);
                    this._showSuccessState(banner);
                } else {
                    console.error('VIDEO REQUEST: Server returned success=false');
                }
            } else {
                console.error('VIDEO REQUEST: Server error:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('VIDEO REQUEST: Network error:', error);
        } finally {
            setTimeout(() => {
                this.isRequestProcessing = false;
                btn.classList.remove('processing');
            }, 500);
        }
    }

    _storeVideoRequestState(videoId) {
        const storageKey = `video_request_hidden_${videoId}`;
        const requestData = {
            timestamp: Date.now(),
            videoId: videoId
        };
        localStorage.setItem(storageKey, JSON.stringify(requestData));
    }

    _showSuccessState(banner) {
        banner.classList.add('video-request-banner--success');
        
        const textElement = banner.querySelector('.video-request-text');
        if (textElement) {
            const successText = banner.dataset.successText || 'Vielen Dank für Ihre Anfrage! Wir werden ein Video zu diesem Thema erstellen.';
            textElement.textContent = successText;
        }
    }

}