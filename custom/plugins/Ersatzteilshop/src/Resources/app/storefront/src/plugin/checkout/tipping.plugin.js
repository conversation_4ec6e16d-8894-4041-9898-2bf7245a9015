import Plugin from 'src/plugin-system/plugin.class';
import DomAccess from 'src/helper/dom-access.helper';
import Iterator from 'src/helper/iterator.helper';
import PageLoadingIndicatorUtil from 'src/utility/loading-indicator/page-loading-indicator.util';

export default class TippingPlugin extends Plugin {
    static options = {
        checkboxSelector: '.js-tip-checkbox',
        tipAmountCustomSelector: '.js-tip-amount-custom',
        tipAmountCustomInputSelector: 'input[name=amount]',
        tipAmountSelector: '.js-tip-amount',
        tipSelectedClass: 'tip-selected',
        creditCardPaymentId: '4673044aff79424a938d42e9847693c3'
    };

    _placeholderCache = '';

    init() {
        const elements = DomAccess.querySelectorAll(document, this.options.tipAmountCustomSelector, false);
        if (elements) {
            Iterator.iterate(elements, element => {
                this._adjustInputWidth(element.querySelector(this.options.tipAmountCustomInputSelector))
            });
        }
        this._registerEvents();

        // Check for success message after page load
        this._checkForSuccessMessage();
    }

    debounce(func, wait) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    _adjustInputWidth(element) {
        let width;
        if (!element.value && element.placeholder) {
            width = element.placeholder.length;
        } else {
            width = element.value.length + 2;
        }
        element.style.width = width + 'ch';
    }

    _onCheckboxCheck(event) {
        if (!event.target.checked) {
            PageLoadingIndicatorUtil.create();
            // request with amount = 0
            const tipAmountCustom = DomAccess.querySelector(document, this.options.tipAmountCustomSelector, false);
            tipAmountCustom.querySelector(this.options.tipAmountCustomInputSelector).value = "0";
            const form = tipAmountCustom.closest('form');
            form.submit();
        }
    }

    _removeSelection() {
        const tipAmounts = DomAccess.querySelectorAll(document, this.options.tipAmountSelector, false);
        if (tipAmounts) {
            Iterator.iterate(tipAmounts, tipAmount => tipAmount.classList.remove(this.options.tipSelectedClass));
        }
    }

    _onTipAmountCustomFocus(event) {
        this._removeSelection();
        this._placeholderCache = event.target.placeholder;
        event.target.placeholder = '';
        this._adjustInputWidth(event.target);
        event.target.parentElement.classList.add(this.options.tipSelectedClass);
    }

    _onTipAmountCustomBlur(event) {
        event.target.placeholder = this._placeholderCache;
        this._adjustInputWidth(event.target);
        if (!event.target.value) {
            event.target.parentElement.classList.remove(this.options.tipSelectedClass);
        }
    }

    _onTipAmountCustomInput(event) {
        PageLoadingIndicatorUtil.create();
        event.target.value = event.target.value.replace(',', '.');

        // Check if credit card payment is selected and add success message
        this._handleCreditCardTipSuccess(event.target.value);

        event.target.closest('form').dispatchEvent(new Event('submit', {
            cancelable: true
        }));
    }

    _onTipAmountClick(event) {
        PageLoadingIndicatorUtil.create();

        // Get the tip amount from the form's hidden input
        const form = event.target.closest('form');
        const amountInput = form.querySelector('input[name="amount"]');
        const tipAmount = amountInput ? amountInput.value : '0';

        // Check if credit card payment is selected and add success message
        this._handleCreditCardTipSuccess(tipAmount);

        event.target.closest('form').dispatchEvent(new Event('submit', {
            cancelable: true
        }));
    }

    _registerEvents() {
        const checkboxes = DomAccess.querySelectorAll(document, this.options.checkboxSelector, false);
        if (checkboxes) {
            Iterator.iterate(checkboxes, checkbox => checkbox.addEventListener('change', this._onCheckboxCheck.bind(this)));
        }

        const tipAmountCustoms = DomAccess.querySelectorAll(document, this.options.tipAmountCustomSelector, false);
        const debouncedInput = this.debounce(this._onTipAmountCustomInput.bind(this), 900);
        if (tipAmountCustoms) {
            Iterator.iterate(tipAmountCustoms, tipAmountCustom => {
                const input = tipAmountCustom.querySelector(this.options.tipAmountCustomInputSelector);
                tipAmountCustom.addEventListener('click', () => input.focus());
                input.addEventListener('focus', this._onTipAmountCustomFocus.bind(this));
                input.addEventListener('blur', this._onTipAmountCustomBlur.bind(this));
                input.addEventListener('input', (event) => {
                    this._adjustInputWidth(input);
                    debouncedInput(event);
                })
            });
        }

        const tipAmounts = DomAccess.querySelectorAll(document, this.options.tipAmountSelector, false);
        if (tipAmounts) {
            Iterator.iterate(tipAmounts, tipAmount => tipAmount.addEventListener('click', this._onTipAmountClick.bind(this)));
        }
    }

    /**
     * Check if credit card payment is selected and handle success message
     * @param {string} tipAmount - The tip amount value
     * @private
     */
    _handleCreditCardTipSuccess(tipAmount = '0') {
        // Get the current payment method from the page data
        const paymentMethodElement = document.querySelector('[data-payment-method-id]');
        if (!paymentMethodElement) {
            return;
        }

        const currentPaymentMethodId = paymentMethodElement.getAttribute('data-payment-method-id');

        // Check if credit card payment is selected
        if (currentPaymentMethodId === this.options.creditCardPaymentId) {
            // Convert tip amount to number and check if it's greater than 0
            const tipValue = parseFloat(tipAmount.replace(',', '.')) || 0;

            // Always scroll for credit card (because CC data gets cleared)
            sessionStorage.setItem('creditCardScroll', 'true');

            // Only show success message if tip amount is greater than 0
            if (tipValue > 0) {
                sessionStorage.setItem('tipSuccessMessage', 'Vielen Dank für das Trinkgeld ans Team');
            }
        }
    }

    /**
     * Check for success message after page load and display it
     * @private
     */
    _checkForSuccessMessage() {
        const successMessage = sessionStorage.getItem('tipSuccessMessage');
        const shouldScroll = sessionStorage.getItem('creditCardScroll');

        // Show success message if available
        if (successMessage) {
            // Remove the message from session storage
            sessionStorage.removeItem('tipSuccessMessage');

            // Create and show the success alert
            this._showSuccessAlert(successMessage);
        }

        // Scroll to top if credit card was used (regardless of message)
        if (shouldScroll) {
            // Remove the scroll flag from session storage
            sessionStorage.removeItem('creditCardScroll');

            setTimeout(() => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }, 1000);
        }
    }

    /**
     * Show success alert message
     * @param {string} message
     * @private
     */
    _showSuccessAlert(message) {
        // Find the alerts container
        const alertsContainer = document.querySelector('.page-checkout-confirm .flashbags') ||
                               document.querySelector('.flashbags') ||
                               document.querySelector('.checkout-confirm-step-one');

        if (!alertsContainer) {
            return;
        }

        // Create the success alert HTML
        const alertHtml = `
            <div class="alert alert-success alert-has-icon" role="alert">
                <span class="icon icon-checkmark-circle">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                        <path fill="#758CA3" fill-rule="evenodd" d="M24 12c0 6.6274-5.3726 12-12 12S0 18.6274 0 12 5.3726 0 12 0s12 5.3726 12 12zM12 2C6.4772 2 2 6.4772 2 12s4.4772 10 10 10 10-4.4772 10-10S17.5228 2 12 2zM7.7071 12.2929 10 14.5858l6.2929-6.293c.3905-.3904 1.0237-.3904 1.4142 0 .3905.3906.3905 1.0238 0 1.4143l-7 7c-.3905.3905-1.0237.3905-1.4142 0l-3-3c-.3905-.3905-.3905-1.0237 0-1.4142.3905-.3905 1.0237-.3905 1.4142 0z"></path>
                    </svg>
                </span>
                <div class="alert-content-container">
                    <div class="alert-content">
                        ${message}
                    </div>
                </div>
            </div>
        `;

        // Insert the alert at the beginning of the container
        alertsContainer.insertAdjacentHTML('afterbegin', alertHtml);
    }
}

