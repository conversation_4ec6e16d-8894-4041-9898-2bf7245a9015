import DataTable from "datatables.net-bs4";

class ApplianceTable {
    constructor(options = {}) {
        this.tables = {};
        this.selectedCategories = new Set();
        this.loadedProducts = {};
        this.productDataCache = new Map();
        this.options = {
            loaderTemplate: `
                <div class="card product-box box-full box-loading">
                   <div class="card-body badge-container-mobile">
                      <a class="card-body badge-container-mobile-content" title="Platzhalterbox">
                         <div class="product-badges">
                         </div>
                         <div class="product-image">
                         </div>
                         <div class="product-info k11-mb-10">
                            <div class="product-data-at-ot k11-my-10 k11-md-mt-0">
                            </div>
                            <div class="product-data-name">
                            </div>
                            <div class="product-data-detail k11-mb-10">
                            </div>
                         </div>
                      </a>
                      <div class="product-price-wrapper">
                         <div class="product-data-price">
                         </div>
                         <div class="product-data-delivery">
                         </div>
                         <div class="product-data-button">
                         </div>
                      </div>
                   </div>
                </div>
            `,
            containerSelector: ".appliance-cached-products",
            searchContainerSelector: "#appliance-search",
            searchInputSelector: "#appliance-search-input",
            categoryFilterSelector: "#appliance-category-filter",
            productUrlSelector: "[data-product-url]",
            defaultSearchTerm: "",
            ...options
        };

        this.searchTerm = new URLSearchParams(window.location.search).get('category');
        this.searchInput = document.querySelector(this.options.searchInputSelector);
        this.targetScrollElement = document.getElementById('product-list-tab');
        this.targetScrollElementMobile = document.getElementById('appliance-search');
        this.isMobile = window.innerWidth < 768;
        this.scrollToTable = false;
        this.currentCategoryFilter = '';

        this.init();
    }

    init() {
        this.registerPaginationExtension();
        this.initCategories();
        this.initTables();
    }

    initTables() {
        const pageNumber = parseInt(new URLSearchParams(window.location.search).get('p')) || 1;
        document.querySelectorAll(this.options.containerSelector).forEach(el => {
            const table = new DataTable(el, {
                paging: true, autoWidth: false, searching: true, processing: false,
                ordering: false, deferRender: true, lengthChange: false, info: false,
                pageLength: 15, pageType: 'full_numbers', displayStart: (pageNumber - 1) * 15,
                language: {
                    paginate: {
                        next:
                            `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9.26855 5.58984L15.6787 12L9.26855 18.4101" stroke="#202E3D" stroke-width="3.84607"/>
                            </svg>`,
                        previous:
                            `<svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9.67871 1.58984L3.26859 7.99996L9.67871 14.4101" stroke="#202E3D" stroke-width="3.84607"/>
                            </svg>`
                    },
                    emptyTable: document.querySelector('#no-products').innerHTML,
                    zeroRecords: document.querySelector('#no-products').innerHTML,
                },
                drawCallback: () => {
                    if (this.scrollToTable) {
                        const targetElement = this.isMobile ? this.targetScrollElementMobile : this.targetScrollElement;
                        if (targetElement) {
                            const yOffset = this.isMobile ? -20 : 0;
                            const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;
                            window.scrollTo({ top: y, behavior: 'smooth' });
                        }
                        this.scrollToTable = false;
                    }
                },
                rowCallback: (row) => {
                    const productContainer = row.querySelector('[data-product-id]');
                    const productId = productContainer.dataset.productId;
                    const parent = productContainer.closest('tr');

                    if (this.productDataCache.has(productId)) {
                        this.renderProduct(productContainer, parent, this.productDataCache.get(productId));
                        return;
                    }

                    parent.classList.add('has-element-loader');
                    productContainer.innerHTML = this.options.loaderTemplate;

                    (window.prefetchedProducts && window.prefetchedProducts[productId] ?
                            Promise.resolve(window.prefetchedProducts[productId]) :
                            fetch(window.quickViewUrl.replace('PRODUCT_ID', productId)).then(response => response.ok ? response.text() : null)
                    ).then(html => {
                        if (!html) {
                            parent.remove();
                            return;
                        }
                        this.productDataCache.set(productId, html);
                        this.renderProduct(productContainer, parent, html);
                    }).catch(e => {
                        console.error(e);
                        parent.remove();
                    });
                },
                renderer: { pageButton: 'appliancePagination' }
            });

            this.tables[el.id] = table;

            let debounceTimer;
            this.searchInput.addEventListener("keyup", (event) => {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    this.performSearch(event.target.value);
                }, 500);
            });

            el.addEventListener('click', (event) => {
                const resetLink = event.target.closest('a.reset-search');
                if (resetLink) {
                    event.preventDefault();
                    this.searchInput.value = '';
                    this.performSearch('');
                }
            });

            if (this.searchTerm) {
                this.searchInput.value = this.searchTerm;
                table.search(this.searchTerm).draw();
                this.searchInput.focus();
                this.searchInput.setSelectionRange(this.searchInput.value.length, this.searchInput.value.length);
                if (table.page.info().recordsDisplay === 0) {
                    this.searchInput.value = '';
                    table.search('').draw();
                    this.searchInput.focus();
                }
            }
        });
    }

    performSearch(searchTerm) {
        Object.values(this.tables).forEach(table => {
            // Bestehende Suche und Kategoriefilter zurücksetzen
            table.search('').columns().search('');

            // Suche über alle Spalten anwenden
            table.search(searchTerm, true, false);

            // Wenn mind. Kategoriefilter aktiv ist und kein Suchbegriff eingegeben wurde oder Suche geleert wurde
            // die Kategoriefilter anwenden
            if (this.currentCategoryFilter && searchTerm.length === 0) {
                table.search(this.currentCategoryFilter, true, false);
            }

            table.draw();
        });
    }

    renderProduct(productContainer, parent, html) {
        productContainer.innerHTML = html;

        if (productContainer.dataset.productType === 'aswo') {
            const productUrl = productContainer.querySelector(this.options.productUrlSelector);
            productUrl.href += window.applianceParam;
        }

        const addToCart = parent.querySelector('[data-add-to-cart]');
        if (addToCart) {
            if (window.PluginManager) {
                try {
                    window.PluginManager.initializePlugin('AddToCart', addToCart);
                } catch (e) {
                    console.error('Error initializing AddToCart plugin:', e);
                }
            } else {
                const pluginInterval = setInterval(() => {
                    if (!window.PluginManager) return;
                    try {
                        window.PluginManager.initializePlugin('AddToCart', addToCart);
                    } catch (e) {
                        console.error('Error initializing AddToCart plugin:', e);
                    }
                    clearInterval(pluginInterval);
                }, 100);
            }
        }

        const parser = new DOMParser();
        const doc = parser.parseFromString(html, "text/html");
        const schemaElement = doc.querySelector("[data-schema-snippet]");
        if (schemaElement) {
            const scriptElement = document.createElement("script");
            scriptElement.type = "application/ld+json";
            scriptElement.textContent = schemaElement.dataset.schemaSnippet;
            document.head.appendChild(scriptElement);
        }

        parent.classList.add('loaded');
        parent.classList.remove('has-element-loader');
    }

    initCategories() {
        if (!window.applianceCategories.url) return;

        fetch(window.applianceCategories.url)
            .then(response => response.json())
            .then(response => {
                const fragment = document.createDocumentFragment();
                const ul = document.createElement('ul');
                ul.className = 'product-search-list';
                ul.style.cssText = 'padding-top: 0px !important; margin-bottom: 0px !important;';

                let categoryCount = 0;
                for (let category in response.categories) {
                    categoryCount++;
                    const li = document.createElement('li');
                    li.className = 'filter-deselected';
                    // Nur die ersten 3 sichtbar auf Mobile
                    if (categoryCount > 3) {
                        li.classList.add('d-none', 'd-md-block');
                    }
                    li.innerHTML = `
                    <span class="link-deselected"
                        data-category-id="${response.categories[category].id}"
                        data-category-name="${response.categories[category].name}">
                        ${response.categories[category].name}
                    </span>
                `;
                    ul.appendChild(li);
                }

                // Füge "alle anzeigen.." Button nach der dritten Kategorie hinzu
                if (categoryCount > 3) {
                    const showAllLi = document.createElement('li');
                    showAllLi.className = 'filter-deselected d-md-none';
                    showAllLi.innerHTML = `
                    <span class="link-deselected show-all-categories">
                        alle anzeigen...
                    </span>
                `;
                    ul.insertBefore(showAllLi, ul.children[3]);
                }

                fragment.appendChild(ul);
                const categoryFilter = document.querySelector(this.options.categoryFilterSelector);
                categoryFilter.innerHTML = '';
                categoryFilter.appendChild(fragment);

                categoryFilter.addEventListener('click', (event) => {
                    const element = event.target.closest('span');
                    if (!element) return;
                    event.preventDefault();

                    // alle anzeigen Logik
                    if (element.classList.contains('show-all-categories')) {
                        element.closest('li').remove();
                        ul.querySelectorAll('li.d-none').forEach(li => {
                            li.classList.remove('d-none');
                        });
                    }
                    // normaler Filter Logik
                    else if (element.classList.contains('link-deselected') || element.classList.contains('link-selected')) {
                        const categoryId = element.dataset.categoryId;
                        if (element.classList.contains('link-selected')) {
                            this.selectedCategories.delete(categoryId);
                            element.classList.replace('link-selected', 'link-deselected');
                            element.closest('li').classList.replace('filter-selected', 'filter-deselected');
                        } else {
                            this.selectedCategories.add(categoryId);
                            element.classList.replace('link-deselected', 'link-selected');
                            element.closest('li').classList.replace('filter-deselected', 'filter-selected');
                        }
                        this.applyCategories();
                    }
                });
            })
            .catch(e => console.error(e));
    }

    applyCategories() {
        const selectedElements = document.querySelectorAll(`${this.options.categoryFilterSelector} .link-selected`);
        let categoryFilter = new Set();
        let categoryNames = [];

        // Ausgewählte Kategorien sammeln
        selectedElements.forEach(element => {
            categoryFilter.add(element.dataset.categoryId);
            categoryNames.push(element.dataset.categoryName);
        });

        const idFilter = [...categoryFilter].join('|');
        const nameFilter = categoryNames.join('|');

        // KategoryId + Kategoryname kombinieren
        this.currentCategoryFilter = idFilter || nameFilter ? `(${idFilter}|${nameFilter})` : '';

        const currentSearchTerm = this.searchInput.value;
        if (currentSearchTerm) {
            // Wir wollen die aktive Suche belassen und nicht durch die Kategoriefilter überschreiben
            // Kategoriefilter werden dann automatisch aktiv, wenn Suche geleert wird
            this.performSearch(currentSearchTerm);
        } else {
            // Wenn keine Suche aktiv ist, nur den Kategoriefilter anwenden
            Object.values(this.tables).forEach(table => {
                if (this.currentCategoryFilter) {
                    table.search(this.currentCategoryFilter, true, false).draw();
                } else {
                    table.search('').draw();
                }
            });
        }
    }

    registerPaginationExtension() {
        DataTable.ext.renderer.pageButton.appliancePagination = (settings, host, idx, buttons, page, pages) => {
            let api = new DataTable.Api(settings);
            let classes = settings.oClasses;
            let lang = settings.oLanguage.oPaginate;
            let aria = settings.oLanguage.oAria.paginate || {};
            let btnDisplay, btnClass, counter = 0;

            const attach = (container, buttons) => {
                let i, ien, node, button;
                let clickHandler = e => {
                    e.preventDefault();
                    if (!e.currentTarget.classList.contains('disabled') && api.page() != e.data.action) {
                        this.scrollToTable = true;
                        api.page(e.data.action).draw('page');
                        const url = `${window.location.origin}${window.location.pathname}?p=${e.data.pageNumber}${window.location.hash}`;
                        window.history.pushState(document.title, document.title, url);

                        if (e.data.pageNumber === 1) {
                            const prevLink = document.head.querySelector('[rel=prev]');
                            if (prevLink) {
                                prevLink.remove();
                            }
                        } else {
                            const prev = url.replace(/p=\d+/, `p=${e.data.pageNumber - 1}`);
                            const prevLink = document.head.querySelector('[rel=prev]');
                            if (prevLink) {
                                prevLink.href = prev;
                            } else {
                                document.head.insertAdjacentHTML('beforeend', `<link rel="prev" href="${prev}">`);
                            }
                        }

                        if (e.data.pageNumber === api.page.info().pages) {
                            const nextLink = document.head.querySelector('[rel=next]');
                            if (nextLink) {
                                nextLink.remove();
                            }
                        } else {
                            const next = url.replace(/p=\d+/, `p=${e.data.pageNumber + 1}`);
                            const nextLink = document.head.querySelector('[rel=next]');
                            if (nextLink) {
                                nextLink.href = next;
                            } else {
                                document.head.insertAdjacentHTML('beforeend', `<link rel="next" href="${next}">`);
                            }
                        }
                    }
                };

                for (i = 0, ien = buttons.length; i < ien; i++) {
                    button = buttons[i];
                    if (Array.isArray(button)) {
                        attach(container, button);
                    } else {
                        btnDisplay = '';
                        btnClass = '';
                        let pageNumber = 1;

                        switch (button) {
                            case 'ellipsis':
                                node = document.createElement('span');
                                node.className = 'page-link-dots';
                                node.innerHTML = '&#x2026;';
                                container[0].appendChild(node);
                                break;
                            case 'first':
                                btnDisplay = lang.sFirst;
                                btnClass = button + (page > 0 ? '' : ' disabled');
                                pageNumber = 1;
                                break;
                            case 'previous':
                                btnDisplay = lang.sPrevious;
                                btnClass = button + (page > 0 ? '' : ' disabled');
                                pageNumber = page;
                                break;
                            case 'next':
                                btnDisplay = lang.sNext;
                                btnClass = button + (page < pages - 1 ? '' : ' disabled');
                                pageNumber = page + 2;
                                break;
                            case 'last':
                                btnDisplay = lang.sLast;
                                btnClass = button + (page < pages - 1 ? '' : ' disabled');
                                pageNumber = pages;
                                break;
                            default:
                                btnDisplay = button + 1;
                                btnClass = page === button ? 'active' : '';
                                pageNumber = button + 1;
                                break;
                        }

                        if (btnDisplay) {
                            node = document.createElement('li');
                            node.className = classes.sPageButton + ' ' + btnClass;
                            node.id = idx === 0 && typeof button === 'string' ? settings.sTableId + '_' + button : null;
                            node.appendChild(document.createElement('a'));
                            node.children[0].href = '#';
                            node.children[0].setAttribute('aria-controls', settings.sTableId);
                            node.children[0].setAttribute('aria-label', aria[button]);
                            node.children[0].setAttribute('data-dt-idx', counter);
                            node.children[0].setAttribute('tabindex', settings.iTabIndex);
                            node.children[0].className = 'page-link';
                            node.children[0].innerHTML = btnDisplay;
                            if (button === 'previous' || button === 'next') {
                                node.children[0].style.cssText = 'line-height: 1.4!important;';
                            }
                            container[0].appendChild(node);

                            settings.oApi._fnBindAction(node, {action: button, pageNumber: pageNumber}, clickHandler);
                            counter++;
                        }
                    }
                }
            };

            let activeEl;
            try {
                activeEl = host.querySelector(document.activeElement).getAttribute('data-dt-idx');
            } catch (e) {}

            host.innerHTML = '<ul class="pagination"></ul>';
            attach(host.children, buttons);

            if (activeEl !== undefined) {
                host.querySelector(`[data-dt-idx="${activeEl}"]`).focus();
            }
        };
    }
}

new ApplianceTable(window.applianceOptions || {});
