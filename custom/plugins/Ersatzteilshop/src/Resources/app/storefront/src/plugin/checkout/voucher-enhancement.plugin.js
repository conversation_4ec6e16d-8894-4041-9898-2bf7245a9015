import Plugin from 'src/plugin-system/plugin.class';
import DomAccess from 'src/helper/dom-access.helper';

export default class VoucherEnhancementPlugin extends Plugin {
    static options = {
        voucherInputSelector: '.voucher-input-enhanced',
        voucherFormSelector: '.cart-add-promotion',
        errorMessageSelector: '.voucher-error-message',
        successClass: 'voucher-success',
        errorClass: 'voucher-error',
        localStorageCodeKey: 'voucher_enhancement_code',
        localStorageScrollKey: 'voucher_scroll_position'
    };

    init() {
        this._voucherInput = DomAccess.querySelector(document, this.options.voucherInputSelector, false);
        this._voucherForm = DomAccess.querySelector(document, this.options.voucherFormSelector, false);
        this._errorMessage = DomAccess.querySelector(document, this.options.errorMessageSelector, false);

        if (!this._voucherInput || !this._voucherForm) {
            return;
        }

        this._registerEvents();
        this._handlePageLoad();
    }

    _registerEvents() {
        // Save scroll position and voucher code before form submission
        this._voucherForm.addEventListener('submit', this._onFormSubmit.bind(this));
        
        // Clear error state when user starts typing
        this._voucherInput.addEventListener('input', this._onInputChange.bind(this));
    }

    _onFormSubmit(event) {
        // Save current scroll position
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        localStorage.setItem(this.options.localStorageScrollKey, scrollPosition.toString());
        
        // Save the voucher code for persistence after page reload
        const code = this._voucherInput.value.trim();
        if (code) {
            localStorage.setItem(this.options.localStorageCodeKey, code);
        }
    }

    _onInputChange() {
        // Clear error state when user starts typing
        this._clearErrorState();
    }

    _handlePageLoad() {
        // Restore scroll position if available
        const savedScrollPosition = localStorage.getItem(this.options.localStorageScrollKey);
        if (savedScrollPosition) {
            // Small delay to ensure page is fully loaded
            setTimeout(() => {
                window.scrollTo(0, parseInt(savedScrollPosition));
                localStorage.removeItem(this.options.localStorageScrollKey);
            }, 100);
        }

        // Get voucher code from localStorage
        const voucherCode = localStorage.getItem(this.options.localStorageCodeKey);
        
        // Set the input value if we have a code
        if (voucherCode) {
            this._voucherInput.value = voucherCode;
            localStorage.removeItem(this.options.localStorageCodeKey);

            // Check for success or error state
            this._checkVoucherStatus();
        }
    }

    _checkVoucherStatus() {
        console.log('Voucher Enhancement: Checking voucher status...');

        // Check for error messages in flash messages first
        const alertDanger = document.querySelector('.alert-danger');
        if (alertDanger) {
            const alertText = alertDanger.textContent || alertDanger.innerText;

            // Check for promotion-related error messages
            if (alertText.includes('not found') ||
                alertText.includes('nicht gefunden') ||
                alertText.includes('ungültig') ||
                alertText.includes('invalid') ||
                alertText.includes('Coupon') ||
                alertText.includes('Gutschein')) {
                this._showErrorState('Dieser Code ist ungültig');
                return;
            }
        }

        // If no error found, assume success and show green border
        this._showSuccessState();

        // Also check for additional success indicators for confirmation
        this._checkForSuccessIndicators();
    }

    _checkForSuccessIndicators() {
        // This method is now just for additional confirmation
        // The main success state is already set in _checkVoucherStatus()

        // Check if there are any promotion line items in the cart
        const cartSummary = document.querySelector('.checkout-aside-summary');
        if (cartSummary) {
            // Look for promotion items in the cart
            const promotionItems = cartSummary.querySelectorAll('[data-line-item-type="promotion"], .line-item-promotion');
            if (promotionItems.length > 0) {
                return true;
            }
        }

        // Alternative: Check for discount in the cart totals
        const cartTotals = document.querySelector('.checkout-aside-summary');
        if (cartTotals) {
            const discountElements = cartTotals.querySelectorAll('.line-item-total-price-discount, .checkout-aside-summary-discount');
            if (discountElements.length > 0) {
                return true;
            }
        }
        return false;
    }

    _showSuccessState() {
        this._clearErrorState();
        this._voucherInput.classList.add(this.options.successClass);
        this._voucherInput.classList.remove(this.options.errorClass);
    }

    _showErrorState(message = 'Dieser Code ist ungültig') {
        this._voucherInput.classList.add(this.options.errorClass);
        this._voucherInput.classList.remove(this.options.successClass);
        
        if (this._errorMessage) {
            this._errorMessage.querySelector('small').textContent = message;
            this._errorMessage.style.display = 'block';
        }
    }

    _clearErrorState() {
        this._voucherInput.classList.remove(this.options.errorClass);
        this._voucherInput.classList.remove(this.options.successClass);
        
        if (this._errorMessage) {
            this._errorMessage.style.display = 'none';
        }
    }
}
