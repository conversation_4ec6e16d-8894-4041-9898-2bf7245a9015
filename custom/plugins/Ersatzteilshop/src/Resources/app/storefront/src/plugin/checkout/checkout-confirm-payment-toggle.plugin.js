import Plugin from 'src/plugin-system/plugin.class';

export default class CheckoutConfirmPaymentToggle extends Plugin {
    init() {
        this._registerToggleEvents();
        this._paymentEdit();
    }

    _registerToggleEvents() {
        const toggleButtons = this.el.querySelectorAll('.payment-method-toggle-button');

        toggleButtons.forEach(toggle => {
            toggle.addEventListener('click', event => {
                event.preventDefault();

                if (toggle.classList.contains('is-selected')) return;

                const wrapper = toggle.closest('.payment-method-wrapper');
                const description = wrapper.nextElementSibling;
                const icon = toggle.querySelector('.payment-method-toggle-icon');

                if (!description || !icon) return;

                const isOpen = !description.classList.contains('d-none');

                description.classList.toggle('d-none');

                icon.outerHTML = isOpen
                    ? `<svg class="payment-method-toggle-icon" xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16" fill="none">
                         <path d="M2.26953 1.58984L8.67965 7.99996L2.26953 14.4101" stroke="#989898" stroke-width="3.84607"/>
                       </svg>`
                    : `<svg class="payment-method-toggle-icon" xmlns="http://www.w3.org/2000/svg" width="17" height="11" viewBox="0 0 17 11" fill="none">
                         <path d="M14.8828 1.79492L8.47269 8.20504L2.06257 1.79492" stroke="#202E3D" stroke-width="3.84607"/>
                       </svg>`;
            });
        });
    }

    _paymentEdit() {
        const paymentEditButton = document.getElementById('svg-back-to-payment');

        if (!paymentEditButton) return;

        paymentEditButton.addEventListener("click", function() {
            window.scrollTo({ top: 100, left: 0, behavior: "smooth" });
        });
    }
}