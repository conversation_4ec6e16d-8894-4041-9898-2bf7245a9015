.form-control, .custom-select {
    height: 42px;
    padding: 10px;
    border-radius: 8px;
    border: 2px solid var(--dunkel-grau, #E0E0E0);
}

.country-select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background: white; /* Hintergrundfarbe nach Wunsch */
    padding-right: 30px; /* Platz für den Pfeil */
    position: relative;

}

.country-select::-ms-expand {
    display: none;
}

/* Eigenen Pfeil mit Background-Image setzen */
.country-select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='16' viewBox='0 0 12 16' fill='none'%3E%3Cpath d='M2.26953 1.58984L8.67965 7.99996L2.26953 14.4101' stroke='%23989898' stroke-width='3.84607'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;

}

/* <PERSON>n das <PERSON> geöffnet ist, Pfeil drehen */
.country-select:focus,
.country-select:active {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='11' viewBox='0 0 16 11' fill='none'%3E%3Cpath d='M14.4102 1.79492L8.00004 8.20504L1.58991 1.79492' stroke='%23202E3D' stroke-width='3.84607'/%3E%3C/svg%3E");
}

.invalid-feedback, .js-validation-message {
    font-size: 100%;
}

.form-control-md-6 {
    display: block;
    width: 49%;
}

.col-address-icon {
    flex: 0 0 25%;
    max-width: 25%;
}

.col-address-info {
    flex: 0 0 75%;
    max-width: 75%;
}

.address-editor-modal {
    .col-address-icon {
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%;
    }

    .col-address-info {
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%;
    }
}

.form-edit-address {
    background-color: #f7f7f7
}

.form-edit-address-left {
    padding: 15px 15px 0px 25px;
}

.form-edit-address-right {
    border-left: 1px solid lightgray;
    padding: 0px 25px 0px 15px;
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.address-type-icon {
    width: 50px;
    height: 50px;
}

.edit_icon_image {
    text-align: left;
}

button[type=submit] {
    .loader {
        width: 20px;
        height: 20px;
        vertical-align: middle;
        border-width: 2px;
        margin-top: -3px;
    }
}

@include media-breakpoint-down(sm) {
    .form-control-md-6 {
        width: 100%;
    }

    .form-edit-address-right {
        align-items: center;
        margin-bottom: 15px;
        border-left: none;
        padding: 0px 25px 0px 25px;

        .btn-block {
            width: 100%;
        }

        > form {
            width: 100%;
        }
    }

    .edit_icon_image {
        text-align: center;
    }

    .confirm-billing-address {
        margin-bottom: 30px;
    }

    .rowToChange {
        margin-bottom: 8vh;
    }
}

.form-control {
    &.is-invalid {
        border-color: #dc3545;
        padding-right: calc(1.5em + .75rem);
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }
}

.is-invalid ~ .invalid-tooltip {
    display: block;
}
