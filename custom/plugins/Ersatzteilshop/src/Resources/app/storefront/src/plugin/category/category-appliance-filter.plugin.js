import Plugin from 'src/plugin-system/plugin.class';
import DomAccess from 'src/helper/dom-access.helper';

export default class CategoryApplianceFilterPlugin extends Plugin {
    static options = {
        searchInputSelector: '#category-search-input',
        resultsContainerSelector: '.search-results-container',
        resultsCollapseSelector: '#categoryApplianceSearchResults',
        filterStatusSelector: '#appliance-filter-status',
        clearFilterSelector: '#clear-appliance-filter',
        hiddenClass: 'd-none',
        debounceTime: 300
    };

    init() {
        this._searchInput = DomAccess.querySelector(this.el, this.options.searchInputSelector);
        this._resultsContainer = DomAccess.querySelector(this.el, this.options.resultsContainerSelector);
        this._resultsCollapse = DomAccess.querySelector(this.el, this.options.resultsCollapseSelector);

        this._categoryId = this.el.dataset.categoryId;
        this._categoryName = this.el.dataset.categoryName;
        this._hasTopAppliances = this.el.dataset.hasTopAppliances === '1';
        this._applianceCategoryId = this.el.dataset.applianceCategoryId;
        this._topAppliancesContainer = this.el.querySelector('.top-appliances-container');
        this._searchRequest = null;
        this._lastSearchQuery = '';

        this._originalListingWrapper = document.querySelector('.cms-element-product-listing-wrapper');
        this._filteredListingWrapper = null;

        this._searchInput.setAttribute('autocomplete', 'off');
        this._updateSearchIcons('initial');
        this._registerEvents();
    }


    _registerEvents() {
        this._searchInput.addEventListener('input', this._onSearchInput.bind(this));
        this._searchInput.addEventListener('focus', this._onSearchFocus.bind(this));
        this._searchInput.addEventListener('blur', this._onSearchBlur.bind(this));

        const form = this._searchInput.closest('form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                e.stopPropagation();
                return false;
            });
        }

        const clearButton = DomAccess.querySelector(this.el, this.options.clearFilterSelector, false);
        if (clearButton) {
            clearButton.addEventListener('click', this._clearFilter.bind(this));
        }

        const searchAgainButton = document.querySelector('#search-again-desktop');
        if (searchAgainButton) {
            searchAgainButton.addEventListener('click', this._handleSearchAgain.bind(this));
        }

        document.addEventListener('click', this._handleDocumentClick.bind(this));
    }

    _handleDocumentClick(event) {
        if (!this.el.contains(event.target)) {
            this._hideDropdown();
        }
    }

    _onSearchFocus() {
        const searchQuery = this._searchInput.value.trim();
        
        if (searchQuery.length === 0 && this._hasTopAppliances && this._topAppliancesContainer) {
            this._showTopAppliances();
        }
    }

    _onSearchBlur() {
        setTimeout(() => {
            if (!this.el.contains(document.activeElement)) {
                this._hideDropdown();
                this._hideTopAppliances();
            }
        }, 200);
    }

    _showTopAppliances() {
        if (this._topAppliancesContainer) {
            this._resultsContainer.innerHTML = '';
            this._resultsContainer.classList.add('d-none');
            this._topAppliancesContainer.classList.remove('d-none');
            this._showDropdown();
            this._attachTopApplianceHandlers();
        }
    }

    _hideTopAppliances() {
        if (this._topAppliancesContainer) {
            this._topAppliancesContainer.classList.add('d-none');
            this._resultsContainer.classList.remove('d-none');
        }
    }

    _attachTopApplianceHandlers() {
        if (this._topAppliancesContainer) {
            const applianceItems = this._topAppliancesContainer.querySelectorAll('.appliance-item');
            applianceItems.forEach(item => {
                item.removeEventListener('click', this._onApplianceSelect.bind(this));
                item.addEventListener('click', this._onApplianceSelect.bind(this));
            });
        }
    }

    async _onSearchInput(event) {
        const searchQuery = event.target.value.trim();

        if (this._searchRequest) {
            this._searchRequest.abort();
        }

        clearTimeout(this._debounceTimeout);

        if (!searchQuery.length) {
            this._resetWidgetState();
            this._restoreOriginalListing();
            
            if (document.activeElement === this._searchInput) {
                if (this._hasTopAppliances && this._topAppliancesContainer) {
                    this._showTopAppliances();
                } else {
                    this._hideDropdown();
                }
            } else {
                this._hideDropdown();
            }
            return;
        }

        this._hideTopAppliances();

        if (searchQuery.length < 3) {
            this._hideDropdown();
            this._updateSearchIcons('initial');
            return;
        }

        this._debounceTimeout = setTimeout(async () => {
            await this._performSearch(searchQuery);
        }, this.options.debounceTime);
    }

    async _performSearch(searchQuery) {
        this._updateSearchIcons('loading');

        try {
            this._searchRequest = new AbortController();
            const response = await fetch('/appliance-input-search/', {
                method: 'POST',
                signal: this._searchRequest.signal,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    searchQuery: searchQuery,
                    searchTerm: this._categoryName,
                    categoryPage: true
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.html && data.html.trim()) {
                this._resultsContainer.innerHTML = data.html;
                this._updateSearchIcons('initial');
                this._showDropdown();
                this._attachResultHandlers();
            } else {
                this._hideDropdown();
                this._updateSearchIcons('no-result');
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                this._hideDropdown();
                this._updateSearchIcons('no-result');
            }
        } finally {
            this._searchRequest = null;
        }
    }

    _attachResultHandlers() {
        const resultItems = this._resultsContainer.querySelectorAll('li.appliance-item');
        resultItems.forEach(item => {
            item.addEventListener('click', this._onApplianceSelect.bind(this));
        });
    }

    async _onApplianceSelect(event) {
        event.preventDefault();
        event.stopPropagation();

        const applianceItem = event.currentTarget;
        const applianceId = applianceItem.dataset.applianceId;
        const seoUrl = applianceItem.dataset.seoUrl || '#';

        if (!applianceId) {
            return;
        }

        this._hideDropdown();

        const desktopInfo = applianceItem.querySelector('.appliance-info-desktop');
        const mobileInfo = applianceItem.querySelector('.appliance-info-mobile');

        let selectedText = '';
        if (window.innerWidth >= 768 && desktopInfo) {
            selectedText = desktopInfo.textContent.trim();
        } else if (mobileInfo) {
            selectedText = mobileInfo.textContent.trim();
        } else {
            selectedText = applianceItem.textContent.trim();
        }

        this._lastSearchQuery = this._searchInput.value;
        this._searchInput.value = selectedText;

        const isTopAppliance = applianceItem.closest('.top-appliances-container') !== null;
        const categoryIdToUse = isTopAppliance && this._applianceCategoryId ? this._applianceCategoryId : this._categoryId;

        await this._filterProductsByAppliance(applianceId, seoUrl, categoryIdToUse);
    }

    async _filterProductsByAppliance(applianceId, seoUrl, categoryId = null) {
        const filterStatus = DomAccess.querySelector(this.el, this.options.filterStatusSelector);
        filterStatus.classList.add(this.options.hiddenClass);
        this._updateSearchIcons('loading');

        try {
            const response = await fetch(`/appliance/${applianceId}/category-products`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    categoryId: categoryId || this._categoryId,
                    page: 1,
                    limit: 100
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                if (data.total === 0) {
                    this._showNoMatchUI(seoUrl);
                } else {
                    this._showMatchUI();
                    
                    if (this._originalListingWrapper) {
                        this._originalListingWrapper.classList.add(this.options.hiddenClass);
                    }

                    if (!this._filteredListingWrapper) {
                        this._filteredListingWrapper = document.createElement('div');
                        this._filteredListingWrapper.className = 'cms-element-product-listing-wrapper filtered-listing-wrapper';
                        this._originalListingWrapper.parentNode.insertBefore(this._filteredListingWrapper, this._originalListingWrapper.nextSibling);
                    }

                    this._filteredListingWrapper.innerHTML = '<div class="cms-element-product-listing">' + data.html + '</div>';
                    const filterStatusEl = DomAccess.querySelector(this.el, this.options.filterStatusSelector);
                    filterStatusEl.classList.add(this.options.hiddenClass);

                    setTimeout(() => {
                        window.PluginManager.initializePlugins();
                    }, 100);
                }
            } else {
                this._showFilterError();
            }
        } catch (error) {
            this._showFilterError();
        }
    }


    _clearFilter() {
        this._searchInput.value = '';
        this._updateSearchIcons('initial');

        const filterStatus = DomAccess.querySelector(this.el, this.options.filterStatusSelector);
        filterStatus.classList.add(this.options.hiddenClass);

        this._restoreOriginalListing();
    }

    _resetWidgetState() {
        const widgetContainer = this.el;
        const form = this._searchInput.closest('form');
        const h2Element = widgetContainer.querySelector('h2.cta-question');
        
        widgetContainer.classList.remove('k11-border-green', 'k11-border-red');
        widgetContainer.classList.add('k11-border');
        
        if (form) {
            form.classList.remove('k11-border-red');
            form.classList.add('k11-border-green');
        }
        
        if (h2Element && h2Element.dataset.originalText) {
            h2Element.innerHTML = h2Element.dataset.originalText;
            h2Element.style.display = 'flex';
            h2Element.style.justifyContent = 'center';
            h2Element.style.alignItems = 'center';
            h2Element.style.flexWrap = '';
        }
        
        this._updateSearchIcons('initial');
        
        const searchAgainButton = document.querySelector('#search-again-desktop');
        if (searchAgainButton) {
            searchAgainButton.classList.remove('k11-pointer');
            searchAgainButton.classList.add('k11-pointer-blocked');
        }
        
        const noMatchRedirect = document.querySelector('#no-match-appliance-redirect-desktop');
        if (noMatchRedirect) {
            noMatchRedirect.classList.add('d-none');
        }
    }

    _restoreOriginalListing() {
        if (this._originalListingWrapper) {
            this._originalListingWrapper.classList.remove(this.options.hiddenClass);
        }

        if (this._filteredListingWrapper) {
            this._filteredListingWrapper.remove();
            this._filteredListingWrapper = null;
        }

        const filterStatus = DomAccess.querySelector(this.el, this.options.filterStatusSelector);
        filterStatus.classList.add(this.options.hiddenClass);

    }

    _showFilterError() {
        const filterStatus = DomAccess.querySelector(this.el, this.options.filterStatusSelector);
        const messageElement = filterStatus.querySelector('.filter-message');

        filterStatus.classList.remove(this.options.hiddenClass);
        messageElement.textContent = 'Fehler beim Filtern der Produkte';

        const clearButton = filterStatus.querySelector(this.options.clearFilterSelector);
        if (clearButton) {
            clearButton.classList.remove(this.options.hiddenClass);
        }
    }

    _updateSearchIcons(state) {
        const container = this.el;
        const initialIcon = container.querySelector('.input-initial-state');
        const loadingIcon = container.querySelector('.input-loading-state');
        const noResultIcon = container.querySelector('.input-no-result-state');
        const noMatchIcon = container.querySelector('.input-no-match-state');
        const isMatchIcon = container.querySelector('.input-is-match-state');

        [initialIcon, loadingIcon, noResultIcon, noMatchIcon, isMatchIcon].forEach(icon => {
            if (icon) icon.classList.add('d-none');
        });

        switch (state) {
            case 'initial':
                if (initialIcon) initialIcon.classList.remove('d-none');
                break;
            case 'loading':
                if (loadingIcon) loadingIcon.classList.remove('d-none');
                break;
            case 'no-result':
                if (noResultIcon) noResultIcon.classList.remove('d-none');
                break;
            case 'no-match':
                if (noMatchIcon) noMatchIcon.classList.remove('d-none');
                break;
            case 'is-match':
                if (isMatchIcon) isMatchIcon.classList.remove('d-none');
                break;
            default:
                if (initialIcon) initialIcon.classList.remove('d-none');
        }
    }

    _showDropdown() {
        this._resultsCollapse.classList.add('show');
        this._resultsCollapse.style.display = 'block';
    }

    _hideDropdown() {
        this._resultsCollapse.classList.remove('show');
        this._resultsCollapse.style.display = 'none';
    }

    _showMatchUI() {
        const widgetContainer = this.el;
        const form = this._searchInput.closest('form');
        const h2Element = widgetContainer.querySelector('h2');
        
        widgetContainer.classList.remove('k11-border', 'k11-border-red');
        widgetContainer.classList.add('k11-border-green');
        
        if (form) {
            form.classList.remove('k11-border-red');
            form.classList.add('k11-border-green');
        }
        if (h2Element) {
            h2Element.innerHTML = `Diese Ersatzteile passen zu Ihrem Gerät`;
        }
        this._updateSearchIcons('is-match');
        
        this._enableSearchAgain();
        const noMatchRedirect = document.querySelector('#no-match-appliance-redirect-desktop');
        if (noMatchRedirect) {
            noMatchRedirect.classList.add('d-none');
        }
    }

    _showNoMatchUI(seoUrl) {
        const widgetContainer = this.el;
        const form = this._searchInput.closest('form');
        const h2Element = widgetContainer.querySelector('h2');
        
        widgetContainer.classList.remove('k11-border', 'k11-border-green');
        widgetContainer.classList.add('k11-border-red');
        
        if (form) {
            form.classList.remove('k11-border-green');
            form.classList.add('k11-border-red');
        }
        
        if (h2Element) {
            h2Element.style.display = 'block';
            h2Element.style.justifyContent = 'center';
            h2Element.style.alignItems = 'center';
            h2Element.style.flexWrap = 'nowrap';
            h2Element.innerHTML = `Leider passt keines dieser Ersatzteile`;
        }
        this._updateSearchIcons('no-match');
        
        this._enableSearchAgain();


        const noMatchRedirect = document.querySelector(
            '#no-match-appliance-redirect-desktop'
        );

        if (noMatchRedirect) {
            const redirectLink = noMatchRedirect.querySelector('a');
            if (redirectLink && seoUrl) {
                let url;
                
                if (seoUrl.includes('://')) {
                    url = new URL(seoUrl);
                } else {
                    const path = seoUrl.startsWith('/') ? seoUrl : `/${seoUrl}`;
                    url = new URL(path, window.location.origin);
                }

                url.searchParams.delete('category');
                if (this._categoryName) {
                    url.searchParams.set('category', this._categoryName);
                }

                redirectLink.href = url.toString();
            }
            noMatchRedirect.classList.remove('d-none');
        }
    }

    _enableSearchAgain() {
        const searchAgainButton = document.querySelector('#search-again-desktop');
        if (searchAgainButton) {
            searchAgainButton.classList.remove('k11-pointer-blocked');
            searchAgainButton.classList.add('k11-pointer');
        }
    }

    _handleSearchAgain() {
        this._resetWidgetState();
        
        if (this._lastSearchQuery) {
            this._searchInput.value = this._lastSearchQuery;
            this._searchInput.focus();
            
            const inputEvent = new Event('input', { bubbles: true, cancelable: true });
            this._searchInput.dispatchEvent(inputEvent);
        }
    }
}