.is-act-confirmpage,
.is-act-editorder {
    .checkout {
        padding-top: 0px;
        .checkout-action {
            @extend .col-lg-8;
            @extend .offset-lg-2;

            margin-top: 20px;
            padding: 20px 0;
        }

        .cart-add-promotion {
            #addPromotionInput {
                height: 100%;
                margin-bottom: 10px;
            }
            #addPromotion {
                display: block;
                width: 100%;
                height: 29px;
                line-height: 0px;
            ;
            }
        }

        .confirm-tos {
            margin-left: 0;

            @media (max-width: 768px) {
               margin-left: 0;
            }

            a {
                color: $color-green;
            }
        }

        .checkout-confirm-tos-label {
            color: var(--Hover, #202E3D);
            font-family: "Helvetica Neue LT Com", sans-serif;
            font-size: 14px;
            font-weight: 400;
            font-style: normal;
            line-height: 1.5; // 150%

            a {
                color: var(--Hover, #202E3D);
                font-family: "Helvetica Neue", sans-serif;
                font-size: 14px;
                font-weight: 500;
                font-style: normal;
                line-height: 1.5;

                text-decoration-line: underline;
                text-decoration-style: solid;
                text-decoration-skip-ink: none;
                text-decoration-thickness: auto;
                text-underline-offset: auto;
                text-underline-position: from-font;
            }
        }

    }

    .checkout-step {
        margin-top: 20px;
        margin-bottom: 35px;
    }

    .container {
        max-width: 1440px;
    }

    .shop-review-widget.on-checkout-page {
        margin-top: 32px;

        a {
            justify-content: center;
        }

        @include media-breakpoint-up(sm) {
            a {
                justify-content: flex-end;
            }
        }
    }

    .payment-method-label,
    .shipping-method-label {

        .payment-method-description,
        .shipping-method-description {
            width: 100%;
        }
    }

    .revocation-notice {
        display: none;
    }

}
.confirm-main-header{
    padding-top: 35px;
    margin-bottom: 2rem;
}
.confirm-shipping {
    .card-title {
        top: 4px;
    }
}

.is-act-confirmpage, .is-act-editorder {
    .confirm-section-title {
        font-weight: 700;
    }

    .payment-methods {
        padding-top: 10px;

        .payment-form-group {
            margin-bottom: 10px;
        }
        .payment-method-description {
            margin-top: 10px;
            padding-top: 10px;
            margin-left: -35px;
        }
        .payment-method-description > p {
            color: black;
        }

        .payment-control label {
            width: 100%;
        }

        .payment-method-image {
            max-height: 24px;
            margin-left: 10px;
            margin-right: 10px;
        }

        .payment-method-toggle-description {
            margin-left: -35px;
        }
    }

    .confirm-address {
        .js-confirm-overview-addresses {
            margin-top: 0px !important;
        }
    }

    .confirm-voucher {
        margin-bottom: 3rem;
        margin-top: -2rem;
    }
    #confirmFormSubmit {
        width: calc(100% - 215px);
        padding: 3px 10px;
    }
}

.tip-container {
    border: 1px solid #E0E0E0;
    display: flex;
    gap: 20px;
    flex-flow: column;
    border-radius: 8px;

    @media (max-width: 768px) {
        margin-top: 20px;
        margin-bottom: 0;
    }
}

.tip-item {
    display: flex;
    gap: 10px;

    &:before {
        display: block;
        content: '\25cf';
    }
}

.tip-grid {
    display: grid;
    gap: 10px;
}

.tip-amount-container {
    display: grid;
    gap: 20px 10px;
    grid-template-columns: repeat(3, 1fr);
    grid-template-areas:
        "a b c"
        "d d d";
}
.tip-amount {
    text-align: center;
    border: 2px solid rgba(116, 203, 123, 0.50);
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    width: 100%;
    background: transparent;

    > .tip-amount-percentage {
        font-weight: 900;
    }

    > * {
        display: block;
        width: 100%;
        padding-bottom: 10px;
        line-height: 100%;
    }

    > .tip-amount-absolute {
        padding-bottom: 0;
    }
}

.tip-amount-custom {
    grid-area: d;

    display: flex;
    justify-content: center;
    align-items: center;

    border: 1px solid #e0e0e0;
    line-height: 150%;

    height: 42px;
    text-align: center;
    border-radius: 8px;
    border: 2px solid #e0e0e0;

    &.tip-selected {
        font-weight: 900;
        input {
            font-weight: 900;
        }
    }

    input {
        border: none;
        height: 100%;
        text-align: center;
        width: 19ch;

        &:focus {
            outline: none;
        }
    }

    .suffix {
        margin-top: 2px;
    }
}

.tip-selected {
    border-color: #202E3D !important;
}

@media screen and (max-width: 1230px) {
    .tip-grid {
        grid-template-columns: repeat(2, min-content);
    }
}

@media screen and (min-width: 1231px) {
    .tip-grid {
        grid-template-columns: repeat(7, min-content);
    }

    .tip-amount-container {
        gap: 20px;
    }
}

@include media-breakpoint-down(sm) {
    .is-act-confirmpage,
    .is-act-editorder {
        #confirmFormSubmit {
            width: 100%;
            height: 50px;
        }
        #confirmOrderForm {
            button {
                &.btn {
                    width: 100%;
                    height: 50px;
                }
            }
        }
        .checkout {
            .checkout-card {
                margin-right: 0px !important;
            }
            .checkout-action {
                padding-left: 10px;
                padding-right: 10px;
            }

            .cart-add-promotion {
                label {
                    display: block;
                    width: 100%;
                }
                #addPromotionInput {
                    display: block;
                    width: 100%;
                    margin-left: 0;
                    margin-bottom: 10px;
                }
                #addPromotion {
                    display: block;
                    width: 100%;
                }
            }

            .confirm-billing-address .row, .confirm-shipping-address .row, .confirm-payment .row, .confirm-shipping .row {
                margin-right: 0;
                margin-left: 0;
            }

            .confirm-billing-address .card-title, .confirm-shipping-address .card-title, .confirm-payment .card-title, .confirm-shipping .card-title {
                padding-right: 0;
                padding-left: 0;
            }
        }

        .checkout-step {
            margin-right: 0px;

            .button-step {
                width: 100%;
                margin-bottom: 5px;
            }
        }
    }
}

#unzer-payment-credit-card-number-input {
  @include media-breakpoint-down(xs) {
    width: 80%;
  }
}

body.is-ctl-checkout .checkout-additional {
    .checkout-main .checkout-additional {
        display: none;
    }

    .page-checkout-aside {
        display: none;
    }
}

.confirm-section-grid {
    display: none !important;
}

.confirm-section-left {
    display: none !important;
}

.confirm-section-right {
    display: none !important;
}

.confirm-address-billing-wrapper,
.confirm-address-shipping-wrapper,
.confirm-voucher,
.confirm-payment-container {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
}

.confirm-address-billing-wrapper {
    margin-bottom: 10px;
}

.confirm-voucher {
    margin-bottom: 0 !important;
}

.confirm-billing-address,
.confirm-shipping-address {
    flex: 0 0 100% !important;
    max-width: 50% !important;

    @media (max-width: 768px) {
        margin-bottom: 0;
        max-width: 100% !important;
    }
}

.confirm-billing-address {
    @media (max-width: 768px) {
        margin-bottom: 10px;
    }
}

.promotion-input-group {
    gap: 20px;

    @media (max-width: 768px) {
        gap: 10px;
    }

    input {
        height: 42px !important;
        flex: 1 1 auto;
    }

    button {
        height: 42px !important;
    }
}

.selected-payment-method {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: -3px;
}

body.is-ctl-checkout.is-act-confirmpage {
    .checkout-aside-summary {
        background-color: #fff !important;
        padding: 0 !important;
    }

    .checkout-aside {
        min-width: 100%;
        max-width: 960px;
        margin-left: 0 !important;
        padding-right: 0px !important;
        padding-left: 0px !important;


        @media (max-width: 768px) {
            margin-left: 0 !important;
        }
    }
}

body.is-ctl-accountorder.is-act-editorder {
    .checkout-aside-summary {
        background-color: #fff !important;
        padding: 0 !important;
    }

    .checkout-aside {
        min-width: 100%;
        max-width: 960px;
        margin-left: 0 !important;
        padding-right: 0px !important;
        padding-left: 0px !important;


        @media (max-width: 768px) {
            margin-left: 0 !important;
        }
    }
}


body.is-ctl-checkout .confirm-address {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}

// Neues Layout: Adresse über ganze Breite, Payment/Voucher darunter
.confirm-address-full-width {
    margin-top: 40px;
    margin-bottom: 40px;
}

// Flexbox Layout für Adressen nebeneinander
.confirm-address-full-width .confirm-address {
    display: flex;
    gap: 20px;
    width: 100%;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: 20px;
    }
}

.confirm-address-billing-section,
.confirm-address-shipping-section {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    flex: 1;
    width: 100% !important;
    box-sizing: border-box;

    @media (max-width: 768px) {
        width: 100%;
        padding: 10px;
    }
}

// Header für Adress-Container
.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.address-header .card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 900;
    color: #202E3D;
}

.address-edit-button {
    display: flex;
    align-items: center;
}

// Payment und Voucher Container nebeneinander
.confirm-payment-voucher-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 30px;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 10px;
    }
}

.confirm-payment-wrapper,
.confirm-voucher-wrapper {
    display: flex;
    flex-direction: column;
}

// Überschriften außerhalb der Container - reduzierte Abstände
.section-title-outside {
    font-size: 18px;
    font-weight: 700;
    color: #202E3D;
    margin-bottom: 12px;
    margin-top: 0;
}

// Container mit Border - gleiche Höhe
.confirm-payment-container,
.confirm-voucher-container {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    height: fit-content;
    min-height: 120px;
}

@media (max-width: 768px) {
    .confirm-payment-container {
        padding: 10px;
        min-height: 0;
    }

    .confirm-voucher-container {
        padding: 10px 10px 0px 10px;
        min-height: 0;
    }
}

// Entferne doppelte Borders und Padding
.confirm-voucher-container .confirm-voucher {
    background: transparent;
    border: none;
    padding: 0;
    margin-bottom: 0 !important;
    margin-top: 0 !important;
}

.confirm-voucher-container .card-body {
    padding: 0;
}

.confirm-shipping-address .card-body {
    flex: none;
}

// Reduzierte Abstände in Payment Container
.confirm-payment-container .card-title {
    margin-bottom: 8px;
}

.confirm-payment-container .selected-payment-method {
    margin-top: 8px !important;
}

// Reduzierte Abstände in Voucher Container
.confirm-voucher-container .card-title {
    margin-bottom: 8px;
}

.confirm-voucher-container .promotion-input-group {
    margin-top: 8px;
}

// Entfernt das Bootstrap grün + weißen Haken
.checkout-confirm-tos-checkbox:checked + .checkout-confirm-tos-label::before {
    background-color: white !important;
    border-color: var(--Hover, #202E3D) !important;
}

.checkout-confirm-tos-checkbox:checked + .checkout-confirm-tos-label::after {
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='21' height='20' viewBox='0 0 21 20' fill='none'><mask id='m' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='21' height='20'><rect x='0.644531' y='0.117188' width='19.7647' height='19.7647' fill='%23D9D9D9'/></mask><g mask='url(%23m)'><path d='M8.51052 14.9401L3.81641 10.2459L4.98994 9.0724L8.51052 12.593L16.0664 5.03711L17.2399 6.21064L8.51052 14.9401Z' fill='%23202E3D'/></g></svg>");
}

// Style das Label als flex-Container für die Pseudoelemente
.checkout-confirm-tos-label {
    position: relative;
    padding-left: 40px; // Platz für die künstliche Checkbox
    cursor: pointer;
    display: inline-block;
    font-family: "Helvetica Neue LT Com", sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 150%;
    color: var(--Hover, #202E3D);

    // Das "Kästchen" vor dem Text
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        width: 28px;
        height: 28px;
        padding: 8.235px;
        justify-content: center;
        align-items: center;
        border-radius: 7px !important;
        border: 2px solid var(--Hover, #E0E0E0);
        background-color: white;
        box-sizing: border-box;
    }

    // Der Haken, erscheint nur wenn checked
    &::after {
        content: '';
        position: absolute;
        top: 0em;
        left: 0;
        display: none;
        width: 28px;
        height: 28px;
        //border: 2px solid var(--Hover, #202E3D);
        justify-content: center;
        align-items: center;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 20px 20px;
        background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='21' height='20' viewBox='0 0 21 20' fill='none'><mask id='m' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='0' y='0' width='21' height='20'><rect x='0.644531' y='0.117188' width='19.7647' height='19.7647' fill='%23D9D9D9'/></mask><g mask='url(%23m)'><path d='M8.51052 14.9401L3.81641 10.2459L4.98994 9.0724L8.51052 12.593L16.0664 5.03711L17.2399 6.21064L8.51052 14.9401Z' fill='%23202E3D'/></g></svg>") !important;
    }

    // Wenn die Checkbox gecheckt ist, zeige den Haken
    .checkout-confirm-tos-checkbox:checked + &::after {
        display: flex;
    }
}

.checkout-confirm-tos-label {
    margin-bottom: 0 !important;

    a {
        color: #202E3D !important;
        font-family: "Helvetica Neue", sans-serif;
        font-size: 14px;
        font-weight: 400 !important;
        font-style: normal;
        line-height: 1;
    }
}

@media (min-width: 992px) {
    .is-act-confirmpage .checkout .checkout-action {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
    }
}

.checkout-action {
    .checkout-buttons {
        display: flex;
        width: 100%;
        gap: 10px;

        @include media-breakpoint-down(sm) {
            flex-direction: column;

            a,
            form {
                width: 100% !important;
                display: block !important;
            }

            a > *,
            form > * {
                width: 100% !important;
            }
        }


        a,
        form {
            flex: 1;
            display: flex;
            gap: 10px;
        }

        a {
            justify-content: center;
            align-items: center;
            height: 42px;
            border-radius: 8px;
            font-weight: 900;
            font-size: 16px;
            background: #E0E0E0;
            color: #202E3D;
            text-decoration: none;
            border: none;
        }

        button {
            flex: 1;
            width: 100%;
            height: 42px !important;
            border-radius: 8px;
            font-weight: 700;
            background-color: #74CB7B;
            color: #202E3D;
            border: none;
        }
    }
}

/* Voucher Input Enhancement Styles */
.voucher-input-wrapper {
    position: relative;
    flex: 1;
}

.voucher-input-enhanced {
    transition: border-color 0.3s ease;

    &.voucher-success {
        border: 2px solid #28a745 !important;
        border-color: #28a745 !important;

        &:focus {
            border-color: #28a745 !important;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
        }
    }

    &.voucher-error {
        border: 2px solid #dc3545 !important;
        border-color: #dc3545 !important;

        &:focus {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }
    }
}

.voucher-error-message {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    margin-top: -7px;

    small {
        font-size: 0.875rem;
        color: #dc3545;
    }
}

/* Ensure the promotion input group has proper spacing for error message */
.promotion-input-group {
    .voucher-input-wrapper {
        margin-right: 10px;
    }
}

/* Button styling adjustments */
.voucher-submit-enhanced {
    height: fit-content;
    align-self: flex-start;
}

.is-act-confirmpage {
    .cart-item-quantity-container {
        margin-top: 10px;
        width: 70px !important;

        .custom-select {
            height: 28px;
            padding: 2px 10px 2px 20px;
            border-radius: 8px;
            border: 2px solid var(--dunkel-grau, #E0E0E0);
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 24'%3E%3Cpath d='M7.52734 9L12.5273 4L17.5273 9H7.52734Z' fill='%231C1B1F'/%3E%3Cpath d='M17.5273 15L12.5273 20L7.52734 15H17.5273Z' fill='%231C1B1F'/%3E%3C/svg%3E") no-repeat right center !important;
        }
    }

    @media (max-width: 768px) {
        .cart-item-quantity-container {
            margin-top: 7px;
        }
        .cart-item-total-price-label,
        .cart-item-unit-price-label,
        .cart-item-quantity-label {
            display: none;
        }
        .cart-item-total-price,
        .cart-item-unit-price,
        .cart-item-quantity {
            display: block !important;

            @media (max-width: 576px) {
                flex: 0 0 33.3333333333% !important;
                max-width: 33.3333333333% !important;
            }
        }
    }
}
