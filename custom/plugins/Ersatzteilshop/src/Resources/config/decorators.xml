<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Shopware\Core\Content\Seo\SeoUrlGenerator" class="Ersatzteilshop\Decorator\Core\Content\Seo\SeoUrlGeneratorDecorator">
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\DefinitionInstanceRegistry"/>
            <argument type="service" id="slugify"/>
            <argument type="service" id="router.default"/>
            <argument type="service" id="request_stack"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Storefront\Controller\AddressController" public="true">
            <argument type="service" id="Shopware\Storefront\Page\Address\Listing\AddressListingPageLoader"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\AccountService"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\ListAddressRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Customer\SalesChannel\UpsertAddressRoute"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Decorator\Storefront\Controller\CartLineItemControllerDecorator"
                 decorates="Shopware\Storefront\Controller\CartLineItemController"
                 decoration-priority="-1">
            <argument type="service" id="Ersatzteilshop\Decorator\Storefront\Controller\CartLineItemControllerDecorator.inner" />

            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Decorator\Storefront\Controller\SearchController"
                 decorates="Swag\EnterpriseSearch\Controller\SearchController" decoration-on-invalid="ignore">
            <argument type="service" id="Ersatzteilshop\Decorator\Storefront\Controller\SearchController.inner"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <argument type="service" id="Swag\EnterpriseSearch\Search\Page\SearchPageLoader"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceManufacturerCategoryRoute"/>
            <argument type="service" id="Ersatzteilshop\Service\CategoryLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\Appliance\ApplianceAggregator"/>
            <argument type="service"
                      id="Ersatzteilshop\Decorator\Core\Content\Product\SalesChannel\Search\ProductSearchRouteDecorator"/>
            <argument type="service" id="XantenAswo\Service\AswoAllocatedProduct" />
            <argument type="service" id="Ersatzteilshop\Storefront\Page\VideoLibrary\VideoLibrarySearchService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Content\Category\SalesChannel\NavigationRouteDecoration" public="true"
                 decorates="Shopware\Core\Content\Category\SalesChannel\NavigationRoute">
            <argument type="service" id="Ersatzteilshop\Decorator\Core\Content\Category\SalesChannel\NavigationRouteDecoration.inner"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="sales_channel.category.repository"/>
            <argument type="service" id="Shopware\Core\Content\Category\SalesChannel\SalesChannelCategoryDefinition"/>
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Search\RequestCriteriaBuilder"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Framework\Adapter\Filesystem\Plugin\CopyBatch" public="true"
                decorates="Shopware\Core\Framework\Adapter\Filesystem\Plugin\CopyBatch">
            <tag name="shopware.filesystem.plugin" />
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Framework\DataAbstractionLayer\ElasticsearchEntityAggregatorHydrator" public="true"
                decorates="Shopware\Elasticsearch\Framework\DataAbstractionLayer\AbstractElasticsearchAggregationHydrator">
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\DefinitionInstanceRegistry"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Framework\Api\EventListener\Authentication\ApiAuthenticationListener"
            decorates="Shopware\Core\Framework\Api\EventListener\Authentication\ApiAuthenticationListener">
            <tag name="kernel.event_subscriber"/>
            <argument type="service" id="shopware.api.resource_server"/>
            <argument type="service" id="shopware.api.authorization_server"/>
            <argument type="service" id="Shopware\Core\Framework\Api\OAuth\UserRepository"/>
            <argument type="service" id="Shopware\Core\Framework\Api\OAuth\RefreshTokenRepository"/>
            <argument type="service" id="Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory"/>
            <argument type="service" id="Shopware\Core\Framework\Routing\RouteScopeRegistry"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Product\ElasticsearchProductDefinition"
                 decorates="Shopware\Elasticsearch\Product\ElasticsearchProductDefinition"
                 decoration-priority="-1">
            <argument type="service" id="Ersatzteilshop\Core\Content\Product\ElasticsearchProductDefinition.inner"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\ApplianceDefinition"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\Indexing\EntityMapper"/>

            <tag name="shopware.es.definition" />
        </service>

        <service id="Ersatzteilshop\Elasticsearch\Client" public="true" decorates="Elasticsearch\Client">
            <factory class="Ersatzteilshop\Elasticsearch\ClientFactory" method="createClient" />
            <argument>%elasticsearch.hosts%</argument>
            <argument>%elasticsearch.username%</argument>
            <argument>%elasticsearch.password%</argument>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Content\Product\SalesChannel\Search\ProductSearchRouteDecorator"
                 decorates="Swag\EnterpriseSearch\Product\ProductSearchRouteAdapter"
                 decoration-on-invalid="ignore"
                 decoration-priority="-1">
            <argument type="service" id="Ersatzteilshop\Decorator\Core\Content\Product\SalesChannel\Search\ProductSearchRouteDecorator.inner"/>
            <tag name="swag_ses.search_gateway" key="product" />
        </service>

        <service id="Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer"
                 class="Ersatzteilshop\Decorator\Elasticsearch\Framework\Indexing\ElasticsearchIndexer">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\ElasticsearchHelper"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\ElasticsearchRegistry"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\Indexing\IndexCreator"/>
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Dbal\Common\IteratorFactory"/>
            <argument type="service" id="Elasticsearch\Client"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="currency.repository"/>
            <argument type="service" id="language.repository"/>
            <argument type="service" id="event_dispatcher"/>
            <argument>%elasticsearch.indexing_batch_size%</argument>
            <tag name="messenger.message_handler"/>
        </service>

        <service id="Shopware\Core\Framework\DataAbstractionLayer\Dbal\Common\IteratorFactory"
                class="Ersatzteilshop\Decorator\Core\Framework\DataAbstractionLayer\Dbal\Common\IteratorFactory">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Elasticsearch\Framework\ElasticsearchHelper"
                 decorates="Shopware\Elasticsearch\Framework\ElasticsearchHelper"
                 decoration-priority="-1"
                 public="true">
            <argument type="service" id="Ersatzteilshop\Decorator\Elasticsearch\Framework\ElasticsearchHelper.inner" />
            <argument>%kernel.environment%</argument>
            <argument type="service" id="logger" />
        </service>

        <service id="Ersatzteilshop\Decorator\Elasticsearch\Framework\DataAbstractionLayer\ElasticsearchEntitySearcher"
                 decorates="Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearcherInterface"
                 decoration-priority="-1">

            <argument type="service" id="Elasticsearch\Client"/>
            <argument type="service"
                      id="Shopware\Elasticsearch\Framework\DataAbstractionLayer\ElasticsearchEntitySearcher.inner"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\ElasticsearchHelper"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\DataAbstractionLayer\CriteriaParser"/>
            <argument type="service"
                      id="Shopware\Elasticsearch\Framework\DataAbstractionLayer\AbstractElasticsearchSearchHydrator"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service
            id="Ersatzteilshop\Decorator\Elasticsearch\Framework\DataAbstractionLayer\ElasticsearchEntityAggregator"
            decorates="Shopware\Core\Framework\DataAbstractionLayer\Search\EntityAggregatorInterface"
            decoration-priority="-1">

            <argument type="service" id="Shopware\Elasticsearch\Framework\ElasticsearchHelper"/>
            <argument type="service" id="Elasticsearch\Client"/>
            <argument type="service"
                      id="Shopware\Elasticsearch\Framework\DataAbstractionLayer\ElasticsearchEntityAggregator.inner"/>
            <argument type="service"
                      id="Shopware\Elasticsearch\Framework\DataAbstractionLayer\AbstractElasticsearchAggregationHydrator"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Elasticsearch\Product\ProductUpdater"
                 decorates="Shopware\Elasticsearch\Product\ProductUpdater"
                 decoration-priority="-1">
            <argument type="service" id="Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer"/>
            <argument type="service" id="Shopware\Core\Content\Product\ProductDefinition"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Elasticsearch\Category\CategoryUpdater"
                decorates="Swag\EnterpriseSearch\Category\CategoryUpdater" decoration-on-invalid="ignore">
            <argument type="service" id="Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer"/>
            <argument type="service" id="Shopware\Core\Content\Category\CategoryDefinition"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Shopware\Core\Checkout\Cart\Rule\LineItemCustomFieldRule" class="Ersatzteilshop\Decorator\Core\Checkout\Cart\Rule\LineItemCustomFieldRuleDecorator">
            <tag name="shopware.rule.definition" priority="-1000"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Framework\Plugin\Util\AssetServiceDecorator"
                 decorates="Shopware\Core\Framework\Plugin\Util\AssetService">
            <argument type="service" id="shopware.filesystem.asset"/>
            <argument type="service" id="kernel"/>
            <argument type="service" id="Shopware\Core\Framework\Plugin\KernelPluginCollection"/>
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <argument>%kernel.shopware_core_dir%</argument>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Content\Category\DataAbstractionLayer\CategoryIndexerDecorator"
                 decorates="Shopware\Core\Content\Category\DataAbstractionLayer\CategoryIndexer">
            <argument type="service"
                      id="Ersatzteilshop\Decorator\Core\Content\Category\DataAbstractionLayer\CategoryIndexerDecorator.inner"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Checkout\Customer\SalesChannel\RegisterRouteDecorator"
                 decorates="Shopware\Core\Checkout\Customer\SalesChannel\RegisterRoute"
                 decoration-priority="1"
                 public="true">
            <argument type="service" id="Ersatzteilshop\Decorator\Core\Checkout\Customer\SalesChannel\RegisterRouteDecorator.inner"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Content\Product\SalesChannel\CrossSelling\ProductCrossSellingRouteDecorator"
                 decorates="Shopware\Core\Content\Product\SalesChannel\CrossSelling\ProductCrossSellingRoute"
                 decoration-priority="-2000">
            <argument type="service" id="product_cross_selling.repository"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="Shopware\Core\Content\ProductStream\Service\ProductStreamBuilder"/>
            <argument type="service" id="sales_channel.product.repository"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingLoader"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Elasticsearch\Product\ElasticsearchSearcher"
                 decorates="Shopware\Elasticsearch\Framework\ElasticsearchHelper">
            <argument type="service" id="Ersatzteilshop\Decorator\Elasticsearch\Product\ElasticsearchSearcher.inner"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Checkout\Payment\SalesChannel\SortedPaymentMethodRouteDecoration"
                 decorates="Shopware\Core\Checkout\Payment\SalesChannel\SortedPaymentMethodRoute"
                 decoration-priority="1"
                 public="true">
            <argument type="service"
                      id="Ersatzteilshop\Decorator\Core\Checkout\Payment\SalesChannel\SortedPaymentMethodRouteDecoration.inner"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Elasticsearch\Indexing\FieldProvider"
                 decorates="Swag\EnterpriseSearch\Indexing\FieldProvider"
                 decoration-on-invalid="ignore"
                 public="true">
            <argument type="service" id="Ersatzteilshop\Decorator\Elasticsearch\Indexing\FieldProvider.inner"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Storefront\Page\Account\Order\AccountEditOrderPageLoaderDecorator"
                 decorates="Shopware\Storefront\Page\Account\Order\AccountEditOrderPageLoader"
                 public="true">
            <argument type="service"
                      id="Ersatzteilshop\Decorator\Storefront\Page\Account\Order\AccountEditOrderPageLoaderDecorator.inner"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Product\SalesChannel\Review\ProductReviewSaveRouteDecorator"
                 decorates="Shopware\Core\Content\Product\SalesChannel\Review\ProductReviewSaveRoute"
                 public="true">
            <argument type="service"
                      id="Ersatzteilshop\Core\Content\Product\SalesChannel\Review\ProductReviewSaveRouteDecorator.inner"/>
            <argument type="service" id="product_review.repository"/>
            <argument type="service" id="category.repository"/>
            <argument type="service" id="Shopware\Core\Framework\Validation\DataValidator"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Symfony\Component\HttpFoundation\RequestStack"/>
            <argument type="service" id="Ersatzteilshop\Service\CustomerMediaService"/>
        </service>

        <service
            id="Ersatzteilshop\Decorator\Pluszwei\ConversionBooster\Services\UpsellProductCart\UpsellProductCartLoader"
            decorates="Pluszwei\ConversionBooster\Services\UpsellProductCart\UpsellProductCartLoader"
            public="true"
            decoration-priority="100"
            decoration-on-invalid="ignore"
        >
            <argument type="service"
                      id="Ersatzteilshop\Decorator\Pluszwei\ConversionBooster\Services\UpsellProductCart\UpsellProductCartLoader.inner"/>
            <argument type="service"
                      id="Shopware\Core\Content\Product\SalesChannel\CrossSelling\ProductCrossSellingRoute"/>
            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService"/>
            <argument type="service" id="product_cross_selling.repository"/>
            <argument type="service" id="Shopware\Core\Content\ProductStream\Service\ProductStreamBuilder"/>
            <argument type="service" id="sales_channel.product.repository"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingLoader"/>
        </service>


        <service id="Ersatzteilshop\Decorator\SwagPayPal\Installment\Banner\InstallmentBannerSubscriber"
                 decorates="Swag\PayPal\Installment\Banner\InstallmentBannerSubscriber"
                 public="true"
                 decoration-on-invalid="ignore">
            <argument type="service"
                      id="Ersatzteilshop\Decorator\SwagPayPal\Installment\Banner\InstallmentBannerSubscriber.inner"/>
        </service>

        <service id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextService"
            class="Ersatzteilshop\Decorator\Core\System\SalesChannel\Context\SalesChannelContextService">
            <argument type="service" id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextFactory"/>
            <argument type="service" id="Shopware\Core\Checkout\Cart\CartRuleLoader"/>
            <argument type="service" id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextPersister"/>
            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService"/>
            <argument type="service" id="request_stack"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Content\Category\SalesChannel\CategoryRouteDecorator"
                 decorates="Shopware\Core\Content\Category\SalesChannel\CategoryRoute" >
            <argument type="service" id="Ersatzteilshop\Decorator\Core\Content\Category\SalesChannel\CategoryRouteDecorator.inner"/>
            <argument type="service" id="category_journey.repository"/>
            <argument type="service" id="category.repository"/>
            <argument type="service" id="Symfony\Component\EventDispatcher\EventDispatcherInterface"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Storefront\Page\Product\Review\ProductReviewLoaderDecorator"
                 decorates="Shopware\Storefront\Page\Product\Review\ProductReviewLoader"
                 public="true">
            <argument type="service"
                      id="Ersatzteilshop\Decorator\Storefront\Page\Product\Review\ProductReviewLoaderDecorator.inner"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Framework\Adapter\Twig\Extension\MediaExtensionDecorator"
                 decorates="Shopware\Core\Framework\Adapter\Twig\Extension\MediaExtension"
                 decoration-on-invalid="ignore">
            <argument type="service"
                      id="Ersatzteilshop\Decorator\Core\Framework\Adapter\Twig\Extension\MediaExtensionDecorator.inner"/>
            <argument type="service" id="media.repository"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Content\Product\DataAbstractionLayer\StockUpdater"
                 decorates="Shopware\Core\Content\Product\DataAbstractionLayer\StockUpdater"
                 decoration-on-invalid="ignore">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="event_dispatcher"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Checkout\Payment\Cart\Token\JWTFactoryV2"
                 decorates="Shopware\Core\Checkout\Payment\Cart\Token\JWTFactoryV2"
                 decoration-on-invalid="ignore">
            <argument type="service" id="Ersatzteilshop\Decorator\Core\Checkout\Payment\Cart\Token\JWTFactoryV2.inner"/>
            <call method="setLogger">
                <argument type="service" id="logger"/>
            </call>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Content\Product\Cart\ProductCartProcessorDecorator"
                 decorates="Shopware\Core\Content\Product\Cart\ProductCartProcessor">
            <argument type="service" id="Ersatzteilshop\Decorator\Core\Content\Product\Cart\ProductCartProcessorDecorator.inner"/>
            <argument type="service" id="Ersatzteilshop\Service\CartService"/>
            <argument type="service" id="Shopware\Core\Checkout\Cart\Price\QuantityPriceCalculator"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Storefront\Framework\Twig\TemplateConfigAccessorDecorator"
                 decorates="Shopware\Storefront\Framework\Twig\TemplateConfigAccessor"
                 public="true">
            <argument type="service" id="Ersatzteilshop\Decorator\Storefront\Framework\Twig\TemplateConfigAccessorDecorator.inner"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Storefront\Theme\ThemeConfigValueAccessor"/>
        </service>

        <service id="Ersatzteilshop\Decorator\Core\Checkout\Order\Validation\OrderValidationFactoryDecorator"
                 decorates="Shopware\Core\Checkout\Order\Validation\OrderValidationFactory"
                 public="true">
            <argument type="service" id="Ersatzteilshop\Decorator\Core\Checkout\Order\Validation\OrderValidationFactoryDecorator.inner"/>
        </service>
    </services>
</container>
