<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>

        <service id="Ersatzteilshop\Subscriber\Pagelet\FooterPagelet">
            <argument type="service" id="Ersatzteilshop\Service\NavigationLoader"/>
            <argument type="service" id="Shopware\Storefront\Theme\ThemeService"/>
            <argument type="service" id="Ersatzteilshop\Service\CachedMenuService"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerWrittenSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer"/>
            <argument type="service"
                      id="Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerDefinition"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\CategoryProductPage">
            <argument type="service" id="category.repository"/>
            <argument type="service" id="product_manufacturer.repository"/>
            <argument type="service" id="sales_channel.product.repository"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingRoute"/>
            <argument type="service" id="Ersatzteilshop\Service\CategoryLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ManufacturerLoader"/>
            <argument type="service" id="category_manufacturer.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\ResourceRuleLoader"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service"
                      id="Ersatzteilshop\Core\Content\ProductManufacturer\ProductManufacturerOverviewRoute"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Listing\ProductListingCriteria">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductATLoader"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Pagelet\HeaderPagelet">
            <argument type="service" id="Ersatzteilshop\Service\CachedMenuService"/>
            <argument type="service" id="Ersatzteilshop\Service\NavigationLoader"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Storefront\Theme\ThemeService"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Register\RegisterBillingAddress">
            <argument type="service" id="country.repository"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\DataAbstractionLayer\ResourceRulePayloadSubscriber">
            <tag name="kernel.event_subscriber"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ResourceRule\DataAbstractionLayer\ResourceRulePayloadUpdater"/>
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheClearer"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ResourceRule\DataAbstractionLayer\ResourceRuleIndexer">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\Framework\DataAbstractionLayer\Dbal\Common\IteratorFactory"/>
            <argument type="service" id="resource_rule.repository"/>
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheClearer"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\ResourceRule\DataAbstractionLayer\ResourceRulePayloadUpdater"/>
            <tag name="shopware.entity_indexer"/>
            <tag name="kernel.event_subscriber" />
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\ProductPage">
            <argument type="service" id="Ersatzteilshop\Service\ResourceRuleLoader"/>
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="twig"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\SchemaOrgService"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingRoute"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\AppliancePage">
            <argument type="service" id="translator"/>
            <argument type="service" id="Ersatzteilshop\Service\VisitedApplianceService"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\RepairGuide\DataAbstractionLayer\RepairGuideSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ErrorGroup\DataAbstractionLayer\ErrorGroupSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\ErrorGroup\DataAbstractionLayer\ErrorCodeSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\CategoryRepairGuidePage">
            <argument type="service" id="category.repository"/>
            <argument type="service" id="Ersatzteilshop\Service\CategoryLoader"/>
            <argument type="service" id="cms_page.repository"/>
            <argument type="service" id="Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoader"/>
            <argument type="service" id="Shopware\Core\Content\Category\SalesChannel\SalesChannelCategoryDefinition"/>
            <argument type="service" id="Ersatzteilshop\Service\RepairGuideLoader"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\CategoryErrorGroupPage">
            <argument type="service" id="Ersatzteilshop\Service\CategoryLoader"/>
            <argument type="service" id="cms_page.repository"/>
            <argument type="service" id="Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoader"/>
            <argument type="service" id="Shopware\Core\Content\Category\SalesChannel\SalesChannelCategoryDefinition"/>
            <argument type="service" id="Ersatzteilshop\Service\ErrorGroupLoader"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\TermsOfUseRatingProductDetailPage">
            <argument id="Shopware\Storefront\Theme\ThemeService" type="service"/>
            <argument id="theme.repository" type="service"/>
            <argument id="Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoader" type="service"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Appliance\DataAbstractionLayer\ApplianceSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\ApplianceDefinition"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Appliance\Validator\ApplianceValidator">
            <argument type="service" id="Doctrine\DBAL\Connection" />
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Product\Validator\ATOTProductValidator">
            <argument type="service" id="Doctrine\DBAL\Connection" />
            <argument  type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Product\Validator\ZeroPriceProductValidator">
            <argument type="service" id="Doctrine\DBAL\Connection" />
            <argument  type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <tag name="kernel.event_subscriber"/>
        </service>

<!--        <service id="Ersatzteilshop\Core\Content\Product\Validator\ExceptionSubscriber">-->
<!--            <argument type="service" id="router"/>-->
<!--            <tag name="kernel.event_subscriber"/>-->
<!--        </service>-->

        <service id="Ersatzteilshop\Subscriber\Page\SearchPage">
            <argument type="service" id="category.repository"/>
            <argument type="service" id="product_manufacturer.repository"/>
            <argument  type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument  type="service" id="event_dispatcher"/>
            <tag name="kernel.event_subscriber"/>
        </service>


        <service id="Ersatzteilshop\Subscriber\Pagelet\MenuOffcanvasPagelet">
            <argument type="service" id="Ersatzteilshop\Service\NavigationLoader"/>
            <argument type="service" id="category.repository"/>
            <tag name="kernel.event_subscriber"/>
        </service>


        <service id="Ersatzteilshop\Subscriber\EntityPreWriteSubscriber">
            <argument type="service" id="request_stack"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\RepairInstruction\DataAbstractionLayer\RepairInstructionSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\LoadAwsoProductApplianceSubscriber">
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\SalesChannel\ApplianceRoute"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\CategoryManufacturer\DataAbstractionLayer\CategoryManufacturerSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <argument type="service" id="Ersatzteilshop\Service\CategoryManufacturerLoader"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\CategoryRepairInstructionPage">
            <argument type="service" id="Ersatzteilshop\Service\CategoryLoader"/>
            <argument type="service" id="cms_page.repository"/>
            <argument type="service" id="Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoader"/>
            <argument type="service" id="Shopware\Core\Content\Category\SalesChannel\SalesChannelCategoryDefinition"/>
            <argument type="service" id="Ersatzteilshop\Service\RepairInstructionLoader"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Auth\Login">
            <argument type="service" id="Ersatzteilshop\Service\CustomerMigrateRoute"/>
            <argument type="service" id="session"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\Category\CategoryIndexerSubscriber">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\GoogleCustomer\GoogleCustomerSubscriber">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="request_stack"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\DisallowRobotsIndexingSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Validation\BuildAddressValidationSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Validation\BuildCustomerProfileValidationSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\DisableHttpCacheSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

<!--        <service id="Ersatzteilshop\Subscriber\Product\StockSubscriber">-->
<!--            <argument type="service" id="logger"/>-->
<!--            <argument type="service" id="Ersatzteilshop\Service\ProductStockSubscriberService"/>-->
<!--            <tag name="kernel.event_subscriber"/>-->
<!--        </service>-->

        <service id="Ersatzteilshop\Core\Content\Appliance\DataAbstractionLayer\ApplianceProductSubscriber">
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\ApplianceDefinition"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer"/>
            <tag name="kernel.event_subscriber"/>
        </service>

<!--        <service id="Ersatzteilshop\Subscriber\HttpCache\ErsatzteilshopCacheResponseSubscriber">-->
<!--            <argument type="service" id="Ersatzteilshop\Service\HttpCacheToken" />-->
<!--            <tag name="kernel.event_subscriber"/>-->
<!--        </service>-->

        <service id="Ersatzteilshop\Subscriber\HttpCache\SideMenuCacheResponseSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Product\DeactivateProductWhenPriceIsZeroSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Register\NewsletterSubscriptionSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Order\CustomerNewsletterSubscriptionSubscriber">
            <argument type="service" id="request_stack" />
            <argument type="service" id="customer.repository"/>
            <argument type="service" id="session"/>
            <call method="setLogger">
                <argument type="service" id="monolog.logger"/>
            </call>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Seo\SeoUrlUpdateSubscriber">
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Product\UpdateTotalApplianceSubscriber">
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Product\StatusUpdateSubscriber">
            <argument type="service" id="product_status_history.repository"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <!-- Appliance Group -->
        <service id="Ersatzteilshop\Core\Content\ApplianceGroup\DataAbstractionLayer\ApplianceGroupSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <argument type="service" id="Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer"/>
            <argument type="service" id="Ersatzteilshop\Core\Content\Appliance\ApplianceDefinition"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Product\ProductFeaturedReCalculateSubscriber">
            <argument type="service" id="Ersatzteilshop\Service\Tracker\ProductFeatureUpdater"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\ABTesting\HttpCacheSubscriber">
            <argument type="service" id="Ersatzteilshop\Util\ABTesting"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\MinimalQuickViewPageSubscriber">
            <argument type="service" id="Ersatzteilshop\Service\ProductReviewLoader"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Product\ProductUpdateMediaSubscriber">
            <argument type="service" id="product_media.repository"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Util\ABTesting">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Seo\SeoDataLoader">
            <argument type="service" id="Ersatzteilshop\Service\SeoGenerator\Client"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Canonical\CanonicalUpdateSubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlPlaceholderHandlerInterface"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Product\DeactivateProductWhenMatchesBlacklistedRuleSubscriber">
            <argument type="service" id="rule.repository"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Shopware\Core\System\SalesChannel\Context\SalesChannelContextFactory"/>

            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Page\ReplaceSpecialWordsFromResponse">
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Search\SearchResultLogging">
            <argument type="service" id="messenger.bus.shopware"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Core\Content\VideoLibrary\DataAbstractionLayer\VideoLibrarySubscriber">
            <argument type="service" id="Shopware\Core\Content\Seo\SeoUrlUpdater"/>
            <argument type="service" id="seo_url.repository"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Order\AttachApplianceIdsSubscriber">
            <argument type="service" id="session" />
            <argument type="service" id="order.repository"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Order\ImageSearchOrderSubscriber">
            <argument type="service" id="session"/>
            <argument type="service" id="ersatzteilshop_image_search_analytics.repository"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="Ersatzteilshop\Subscriber\Validation\RemoveTosValidationSubscriber">
            <tag name="kernel.event_subscriber"/>
        </service>


    </services>

</container>
