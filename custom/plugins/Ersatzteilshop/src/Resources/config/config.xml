<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>Order Settings</title>
        <title lang="de-DE">Order Konfiguration</title>
        <input-field type="bool">
            <name>sendOrderApi</name>
            <label>Enable Sending Order Webhook</label>
            <label lang="de-DE">Enable Sending Order Webhook</label>
            <required>false</required>
        </input-field>

        <input-field>
            <name>repairItemId</name>
            <label>Sendingblue Repair ItemId</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>Manufacturer Settings</title>
        <title lang="de-DE">Manufacturer Konfiguration</title>
        <component name="sw-entity-multi-id-select">
            <name>manufacturersHidden</name>
            <entity>product_manufacturer</entity>
            <label>Hide manufacturers</label>
            <required>false</required>
        </component>
    </card>

    <card>
        <title>Search Settings</title>
        <title lang="de-DE">Search Konfiguration</title>
        <input-field>
            <name>switchLayoutResultRange</name>
            <label>Result range to switch layout</label>
            <label lang="de-DE">Result range to switch layout</label>
            <required>true</required>
        </input-field>

        <input-field type="bool">
            <name>enableSearchSuggest</name>
            <label>Enable Suggested Search</label>
            <label lang="de-DE">Suchvorschläge aktivieren</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>AI Image Search</title>
        <title lang="de-DE">KI Bildsuche</title>

        <input-field type="single-select">
            <name>selectedAIAnalyzer</name>
            <label>Select AI Service</label>
            <label lang="de-DE">KI-Service auswählen</label>
            <options>
                <option>
                    <id>anthropic</id>
                    <name>Anthropic AI</name>
                    <name lang="de-DE">Anthropic AI</name>
                </option>
                <option>
                    <id>gemini</id>
                    <name>Google Gemini</name>
                    <name lang="de-DE">Google Gemini</name>
                </option>
            </options>
            <defaultValue>anthropic</defaultValue>
            <required>true</required>
        </input-field>
        <input-field type="bool">
            <name>showAIScanner</name>
            <label>Enable AI Scanner</label>
            <label lang="de-DE">KI Bildsuche anzeigen</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>Cookie Permission</title>
        <title lang="de-DE">Cookie Permission</title>
        <input-field type="bool">
            <name>autoAcceptCookies</name>
            <label>Enable Automatic Accept Cookies</label>
            <label lang="de-DE">Enable Automatic Accept Cookies</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>Elasticsearch</title>
        <title lang="de-DE">Elastic Search</title>
        <input-field type="bool">
            <name>hideApplianceNoProduct</name>
            <label>Exclude appliances with no product from the search</label>
            <label lang="de-DE">Exclude appliances with no product from the search</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>AT / OT</title>

        <component name="sw-entity-single-select">
            <name>atotPropertyGroup</name>
            <entity>property_group</entity>
            <label>Badge</label>
            <required>true</required>
        </component>

        <component name="sw-entity-single-select">
            <name>otPropertyOption</name>
            <entity>property_group_option</entity>
            <label>OT Property Option</label>
            <required>true</required>
        </component>

        <component name="sw-entity-single-select">
            <name>atPropertyOption</name>
            <entity>property_group_option</entity>
            <label>AT Property Option</label>
            <required>true</required>
        </component>

        <input-field type="textarea">
            <name>aswoAtOtManufacturers</name>
            <label>ASWO Manufacturers for AT/OT</label>
            <label lang="de-DE">ASWO Hersteller für AT/OT</label>
            <helpText>Must be in json format</helpText>
            <helpText lang="de-DE">Muss im json-Format vorliegen</helpText>
        </input-field>

    </card>

    <card>
        <title>Google Customer Settings</title>
        <title lang="de-DE">Google Customer Konfiguration</title>

        <input-field>
            <name>googleParameterKey</name>
            <label>Google Get Parameter Key</label>
            <label lang="de-DE">Google Get Parameter Key</label>
            <required>true</required>
            <placeholder>t</placeholder>
        </input-field>

        <input-field>
            <name>googleParameterValue</name>
            <label>Google Get Parameter Value</label>
            <label lang="de-DE">Google Get Parameter Value</label>
            <required>true</required>
            <placeholder>eadwords</placeholder>
        </input-field>

        <input-field type="single-select">
            <name>googleStoreMode</name>
            <label>Store Mode</label>
            <helpText>If you select the cookie, please set the cookie time life.</helpText>
            <options>
                <option>
                    <id>session</id>
                    <name>session</name>
                </option>
                <option>
                    <id>cookie</id>
                    <name>cookie</name>
                </option>
            </options>
        </input-field>

        <input-field>
            <name>cookieTtl</name>
            <label>Cookie time life</label>
            <required>false</required>
            <placeholder>1 day</placeholder>
        </input-field>

        <component name="sw-entity-single-select">
            <name>googleCustomerGroupId</name>
            <entity>customer_group</entity>
            <label>Google customer group</label>
            <required>true</required>
        </component>
    </card>

    <card>
        <title>Address Validation Settings</title>
        <title lang="de-DE">Google API Key</title>
        <input-field>
            <name>googleApiKey</name>
            <label>Google API Key</label>
            <label lang="de-DE">Google API Key</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>Top Seller Products</title>
        <title lang="de-DE">Top Seller Products</title>
        <input-field>
            <name>dayCalculateProductSold</name>
            <label>Days to calculate product sold</label>
            <label lang="de-DE">Days to calculate product sold</label>
            <required>false</required>
            <placeholder>30</placeholder>
        </input-field>
    </card>

    <card>
        <title>Featured Products</title>
        <title lang="de-DE">Featured Products</title>
        <input-field>
            <name>productFeaturePeriod</name>
            <label>Days to calculate featured product</label>
            <label lang="de-DE">Days to calculate featured product</label>
            <required>true</required>
            <placeholder>30</placeholder>
        </input-field>
        <input-field>
            <name>productFeatureClicks</name>
            <label>Clicks to calculate featured product</label>
            <label lang="de-DE">Clicks to calculate featured product</label>
            <helpText>The products have less than this number will be ignored.</helpText>
            <required>true</required>
            <placeholder>5</placeholder>
        </input-field>
        <input-field type="textarea">
            <name>productFeatureDeliveryWeight</name>
            <label>Delivery weight to calculate featured product</label>
            <label lang="de-DE">Liefergewicht zur Berechnung des angebotenen Produkts</label>
            <helpText>Must be in json format</helpText>
            <helpText lang="de-DE">Muss im json-Format vorliegen</helpText>
            <placeholder>{"delivery_time_id_1": {"image": image_weight1, "no_image": no_image_weight1}, "delivery_time_id_2": {"image": image_weight2, "no_image": no_image_weight2}}</placeholder>
        </input-field>
        <input-field>
            <name>productFeatureChunkSize</name>
            <label>Chunk size</label>
            <label lang="de-DE">Chunk size</label>
            <required>true</required>
            <placeholder>500</placeholder>
        </input-field>
        <input-field>
            <name>productFeatureProperty</name>
            <label>Enter ID of Universal Property</label>
            <label lang="de-DE">Die ID von der Universellen Produkteigenschaft einfügen</label>
            <helpText>The products with this property id will have a higher featured value</helpText>
            <required>false</required>
            <placeholder>2a3ce87c09934ba38f8825cba4847c19</placeholder>
        </input-field>
    </card>

    <card>
        <title>Relevance Appliances</title>
        <title lang="de-DE">Relevance Appliances</title>
        <input-field>
            <name>applianceRelevancePeriod</name>
            <label>Days to calculate relevance appliances</label>
            <label lang="de-DE">Days to calculate relevance appliances</label>
            <required>true</required>
            <placeholder>30</placeholder>
        </input-field>
        <input-field>
            <name>applianceRelevanceClicks</name>
            <label>Clicks to calculate relevance appliances</label>
            <label lang="de-DE">Clicks to calculate relevance appliances</label>
            <helpText>The appliance click have less than this number will be ignored.</helpText>
            <required>true</required>
            <placeholder>5</placeholder>
        </input-field>
        <input-field>
            <name>applianceRelevanceChunkSize</name>
            <label>Chunk size</label>
            <label lang="de-DE">Chunk size</label>
            <required>true</required>
            <placeholder>500</placeholder>
        </input-field>
    </card>

    <card>
        <title>Relevance Categories</title>
        <title lang="de-DE">Relevance Categories</title>
        <input-field>
            <name>categoryRelevancePeriod</name>
            <label>Days to calculate relevance categories</label>
            <label lang="de-DE">Days to calculate relevance categories</label>
            <required>true</required>
            <placeholder>30</placeholder>
        </input-field>
        <input-field>
            <name>categoryRelevanceClicks</name>
            <label>Clicks to calculate relevance categories</label>
            <label lang="de-DE">Clicks to calculate relevance categories</label>
            <helpText>The category click have less than this number will be ignored.</helpText>
            <required>true</required>
            <placeholder>5</placeholder>
        </input-field>
        <input-field>
            <name>categoryRelevanceChunkSize</name>
            <label>Chunk size</label>
            <label lang="de-DE">Chunk size</label>
            <required>true</required>
            <placeholder>500</placeholder>
        </input-field>
    </card>

    <card>
        <title>Performance</title>
        <input-field type="bool">
            <name>rocketLoader</name>
            <label>Rocket Loader</label>
        </input-field>

        <input-field type="int">
            <name>numberApplianceLoaded</name>
            <label>The number of appliances to be loaded on product detail page</label>
            <label lang="de-DE">The number of appliances to be loaded on product detail page</label>
            <defaultValue>100</defaultValue>
            <required>false</required>
        </input-field>

    </card>

    <card>
        <title>Sitemap</title>
        <title lang="de-DE">Sitemap</title>
        <input-field type="int">
            <name>sitemapUrlThreshold</name>
            <label>Sitemap URL Threshold</label>
            <label lang="de-DE">Sitemap URL Threshold</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>ASWO Loading</title>
        <title lang="de-DE">ASWO Loading</title>
        <input-field type="int">
            <name>minModelCodeLength</name>
            <label>Only Load ASWO Products on Appliance Pages if Modecodelength is greater or equal to the minModelCodeLength</label>
            <label lang="de-DE">Lädt ASWO Produkte nur, wenn der Modellcodelänge größer/gleich des gespeicherten Wertes ist</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>AB Testing</title>
        <input-field>
            <name>googleOptimizeKey</name>
            <label>Google Optimize Key</label>
            <required>false</required>
        </input-field>

        <input-field>
            <name>lazyPaypalButtonExperimentID</name>
            <label>Lazy Paypal Button Experiment ID</label>
            <required>false</required>
        </input-field>
    </card>

    <card>

        <title>AB Testing Config</title>
        <title lang="de-DE">AB Testing Config</title>
        <input-field type="int">
            <name>lazyPaypalButtonPercentage</name>
            <label>What % of the user shall see the lazy paypal button?</label>
            <label lang="de-DE">Wie viel Prozent der Nutzer sollen die faule Paypal-Schaltfläche sehen?</label>
            <required>false</required>
        </input-field>

    </card>

    <card>
        <title>Validation</title>
        <title lang="de-DE">Validation</title>

        <input-field type="bool">
            <name>enableValidationOTProduct</name>
            <label>Product - Enable validation OT product</label>
            <label lang="de-DE">>Product - Enable validation OT product</label>
            <required>false</required>
        </input-field>

        <input-field type="bool">
            <name>validationZeroPriceProduct</name>
            <label>Product - Enable validation zero-price product</label>
            <label lang="de-DE">>Product - Enable validation zero-price product</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>Dynamic Product Slider ID</title>
        <title lang="de-DE">Dynamic Product Slider ID</title>
        <input-field>
            <name>sliderID</name>
            <label>Dynamic Product Slider ID</label>
            <label lang="de-DE">Dynamic Product Slider ID</label>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>Household Category</title>
        <title lang="de-DE">Household Category</title>
        <component name="sw-entity-single-select">
            <name>householdCategory</name>
            <entity>category</entity>
            <label>Household Category</label>
            <required>false</required>
        </component>

        <title>Kitchen Category</title>
        <title lang="de-DE">Kitchen Category</title>
        <component name="sw-entity-single-select">
            <name>kitchenCategory</name>
            <entity>category</entity>
            <label>Kitchen Category</label>
            <required>false</required>
        </component>

        <title>E-Mobility Categories</title>
        <title lang="de-DE">Cleaning Category</title>
        <component name="sw-entity-single-select">
            <name>emobilityCategory</name>
            <entity>category</entity>
            <label>E-Mobility Category</label>
            <required>false</required>
        </component>
    </card>

    <card>
        <title>Inactive Products History</title>
        <title lang="de-DE">Inactive Products History</title>
        <input-field type="int">
            <name>productStatusHistoryDays</name>
            <label>Show history for</label>
            <label lang="de-DE">Show history for</label>
            <defaultValue>7</defaultValue>
            <placeholder>7 days</placeholder>
            <required>false</required>
        </input-field>
    </card>

    <card>
        <title>Canonical Settings</title>
        <title lang="de-DE">Canonical Konfiguration</title>
        <component name="sw-entity-multi-id-select">
            <name>canonicalManufacturer</name>
            <entity>product_manufacturer</entity>
            <label>Canonical Manufacturer</label>
            <required>false</required>
        </component>
    </card>

    <card>
        <title>Blacklisted products</title>
        <title lang="de-DE">Blacklisted products</title>
        <component name="sw-entity-single-select">
            <name>blacklistedProductRule</name>
            <entity>rule</entity>
            <label>Rule to apply</label>
            <helpText>The products which match this rule will be always offline</helpText>
            <required>false</required>
        </component>
    </card>

    <card>
        <title>Product assignment</title>
        <title lang="de-DE">Produktzuweisungen</title>
        <component name="sw-entity-single-select">
            <name>tipProduct</name>
            <entity>product</entity>
            <label>Choose the tip product</label>
            <label lang="de-DE">Wählen Sie das Trinkgeld-Produkt</label>
        </component>

        <component name="sw-entity-single-select">
            <name>prioProduct</name>
            <entity>product</entity>
            <label>Choose the express processing product</label>
            <label lang="de-DE">Wählen Sie das Express-Bearbeitung-Produkt</label>
        </component>

        <component name="sw-entity-multi-id-select">
            <name>prioCountries</name>
            <entity>country</entity>
            <label>Choose the countries that allow prioritized processing (whitelist)</label>
            <label lang="de-DE">Wählen Sie alle erlaubten Länder für die Prio-Bearbeitung (Whitelist)</label>
        </component>
    </card>

    <card>
        <title>Country blocks</title>
        <title lang="de-DE">Ländersperre</title>
        <component name="sw-entity-multi-id-select">
            <name>blockTags</name>
            <entity>tag</entity>
            <label>Choose the tags that initiate country locks</label>
            <label lang="de-DE">Wählen Sie die Tags, die für die Ländersperre greifen</label>
        </component>
        <component name="sw-entity-multi-id-select">
            <name>blockCountries</name>
            <entity>country</entity>
            <label>Choose the countries that are allowed when products have one of the given tags</label>
            <label lang="de-DE">Wählen Sie alle erlaubten Länder für Produkte, die eines der Tags zugewiesen haben</label>
        </component>
    </card>
</config>
