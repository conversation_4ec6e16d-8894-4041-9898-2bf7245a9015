(()=>{"use strict";var e={735:e=>{var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===n}(e)}(e)},n="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function i(e,t){return!1!==t.clone&&t.isMergeableObject(e)?o((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function r(e,t,n){return e.concat(t).map((function(e){return i(e,n)}))}function s(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return e.propertyIsEnumerable(t)})):[]}(e))}function a(e,t){try{return t in e}catch(e){return!1}}function o(e,n,u){(u=u||{}).arrayMerge=u.arrayMerge||r,u.isMergeableObject=u.isMergeableObject||t,u.cloneUnlessOtherwiseSpecified=i;var c=Array.isArray(n);return c===Array.isArray(e)?c?u.arrayMerge(e,n,u):function(e,t,n){var r={};return n.isMergeableObject(e)&&s(e).forEach((function(t){r[t]=i(e[t],n)})),s(t).forEach((function(s){(function(e,t){return a(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,s)||(a(e,s)&&n.isMergeableObject(t[s])?r[s]=function(e,t){if(!t.customMerge)return o;var n=t.customMerge(e);return"function"==typeof n?n:o}(s,n)(e[s],t[s],n):r[s]=i(t[s],n))})),r}(e,n,u):i(n,u)}o.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return o(e,n,t)}),{})};var u=o;e.exports=u}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var s=t[i]={exports:{}};return e[i](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var i=n(735),r=n.n(i);class s{static ucFirst(e){return e.charAt(0).toUpperCase()+e.slice(1)}static lcFirst(e){return e.charAt(0).toLowerCase()+e.slice(1)}static toDashCase(e){return e.replace(/([A-Z])/g,"-$1").replace(/^-/,"").toLowerCase()}static toLowerCamelCase(e,t){const n=s.toUpperCamelCase(e,t);return s.lcFirst(n)}static toUpperCamelCase(e,t){return t?e.split(t).map((e=>s.ucFirst(e.toLowerCase()))).join(""):s.ucFirst(e.toLowerCase())}static parsePrimitive(e){try{return/^\d+(.|,)\d+$/.test(e)&&(e=e.replace(",",".")),JSON.parse(e)}catch(t){return e.toString()}}}class a{static isNode(e){return"object"==typeof e&&null!==e&&(e===document||e===window||e instanceof Node)}static hasAttribute(e,t){if(!a.isNode(e))throw new Error("The element must be a valid HTML Node!");return"function"==typeof e.hasAttribute&&e.hasAttribute(t)}static getAttribute(e,t,n=!0){if(n&&!1===a.hasAttribute(e,t))throw new Error(`The required property "${t}" does not exist!`);if("function"==typeof e.getAttribute)return e.getAttribute(t);if(n)throw new Error("This node doesn't support the getAttribute function!")}static getDataAttribute(e,t,n=!0){const i=t.replace(/^data(|-)/,""),r=s.toLowerCamelCase(i,"-");if(!a.isNode(e)){if(n)throw new Error("The passed node is not a valid HTML Node!");return}if(void 0===e.dataset){if(n)throw new Error("This node doesn't support the dataset attribute!");return}const o=e.dataset[r];if(void 0===o){if(n)throw new Error(`The required data attribute "${t}" does not exist on ${e}!`);return o}return s.parsePrimitive(o)}static querySelector(e,t,n=!0){if(n&&!a.isNode(e))throw new Error("The parent node is not a valid HTML Node!");const i=e.querySelector(t)||!1;if(n&&!1===i)throw new Error(`The required element "${t}" does not exist in parent node!`);return i}static querySelectorAll(e,t,n=!0){if(n&&!a.isNode(e))throw new Error("The parent node is not a valid HTML Node!");let i=e.querySelectorAll(t);if(0===i.length&&(i=!1),n&&!1===i)throw new Error(`At least one item of "${t}" must exist in parent node!`);return i}}class o{constructor(e=document){this._el=e,e.$emitter=this,this._listeners=[]}publish(e,t={},n=!1){const i=new CustomEvent(e,{detail:t,cancelable:n});return this.el.dispatchEvent(i),i}subscribe(e,t,n={}){const i=this,r=e.split(".");let s=n.scope?t.bind(n.scope):t;if(n.once&&!0===n.once){const t=s;s=function(n){i.unsubscribe(e),t(n)}}return this.el.addEventListener(r[0],s),this.listeners.push({splitEventName:r,opts:n,cb:s}),!0}unsubscribe(e){const t=e.split(".");return this.listeners=this.listeners.reduce(((e,n)=>n.splitEventName.sort().toString()===t.sort().toString()?(this.el.removeEventListener(n.splitEventName[0],n.cb),e):(e.push(n),e)),[]),!0}reset(){return this.listeners.forEach((e=>{this.el.removeEventListener(e.splitEventName[0],e.cb)})),this.listeners=[],!0}get el(){return this._el}set el(e){this._el=e}get listeners(){return this._listeners}set listeners(e){this._listeners=e}}class u{constructor(e,t={},n=!1){if(!a.isNode(e))throw new Error("There is no valid element given.");this.el=e,this.$emitter=new o(this.el),this._pluginName=this._getPluginName(n),this.options=this._mergeOptions(t),this._initialized=!1,this._registerInstance(),this._init()}init(){throw new Error(`The "init" method for the plugin "${this._pluginName}" is not defined.`)}update(){}_init(){this._initialized||(this.init(),this._initialized=!0)}_update(){this._initialized&&this.update()}_mergeOptions(e){const t=s.toDashCase(this._pluginName),n=a.getDataAttribute(this.el,`data-${t}-config`,!1),i=a.getAttribute(this.el,`data-${t}-options`,!1),o=[this.constructor.options,this.options,e];n&&o.push(window.PluginConfigManager.get(this._pluginName,n));try{i&&o.push(JSON.parse(i))}catch(e){throw console.error(this.el),new Error(`The data attribute "data-${t}-options" could not be parsed to json: ${e.message}`)}return r().all(o.filter((e=>e instanceof Object&&!(e instanceof Array))).map((e=>e||{})))}_registerInstance(){window.PluginManager.getPluginInstancesFromElement(this.el).set(this._pluginName,this),window.PluginManager.getPlugin(this._pluginName,!1).get("instances").push(this)}_getPluginName(e){return e||(e=this.constructor.name),e}}class c{static iterate(e,t){if(e instanceof Map)return e.forEach(t);if(Array.isArray(e))return e.forEach(t);if(!(e instanceof FormData)){if(e instanceof NodeList)return e.forEach(t);if(e instanceof HTMLCollection)return Array.from(e).forEach(t);if(e instanceof Object)return Object.keys(e).forEach((n=>{t(e[n],n)}));throw new Error(`The element type ${typeof e} is not iterable!`)}for(var n of e.entries())t(n[1],n[0])}}const l="loader",d="before";class h{constructor(e,t=d){this.parent=e instanceof Element?e:document.body.querySelector(e),this.position=t}create(){this.exists()||("inner"!==this.position?this.parent.insertAdjacentHTML(this._getPosition(),h.getTemplate()):this.parent.innerHTML=h.getTemplate())}remove(){const e=this.parent.querySelectorAll(`.${l}`);c.iterate(e,(e=>e.remove()))}exists(){return this.parent.querySelectorAll(`.${l}`).length>0}_getPosition(){return this.position===d?"afterbegin":"beforeend"}static getTemplate(){return`<div class="${l}" role="status">\n                    <span class="visually-hidden">Loading...</span>\n                </div>`}static SELECTOR_CLASS(){return l}}const m="element-loader-backdrop";class p extends h{static create(e){e.classList.add("has-element-loader"),p.exists(e)||(p.appendLoader(e),setTimeout((()=>{const t=e.querySelector(`.${m}`);t&&t.classList.add("element-loader-backdrop-open")}),1))}static remove(e){e.classList.remove("has-element-loader");const t=e.querySelector(`.${m}`);t&&t.remove()}static exists(e){return e.querySelectorAll(`.${m}`).length>0}static getTemplate(){return`\n        <div class="${m}">\n            <div class="loader" role="status">\n                <span class="visually-hidden">Loading...</span>\n            </div>\n        </div>\n        `}static appendLoader(e){e.insertAdjacentHTML("beforeend",p.getTemplate())}}class y{constructor(){this._request=null}get(e,t,n="application/json"){const i=this._createPreparedRequest("GET",e,n);return this._sendRequest(i,null,t)}post(e,t,n,i="application/json"){i=this._getContentType(t,i);const r=this._createPreparedRequest("POST",e,i);return this._sendRequest(r,t,n)}delete(e,t,n,i="application/json"){i=this._getContentType(t,i);const r=this._createPreparedRequest("DELETE",e,i);return this._sendRequest(r,t,n)}patch(e,t,n,i="application/json"){i=this._getContentType(t,i);const r=this._createPreparedRequest("PATCH",e,i);return this._sendRequest(r,t,n)}abort(){if(this._request)return this._request.abort()}_registerOnLoaded(e,t){t&&e.addEventListener("loadend",(()=>{t(e.responseText,e)}))}_sendRequest(e,t,n){return this._registerOnLoaded(e,n),e.send(t),e}_getContentType(e,t){return e instanceof FormData&&(t=!1),t}_createPreparedRequest(e,t,n){return this._request=new XMLHttpRequest,this._request.open(e,t),this._request.setRequestHeader("X-Requested-With","XMLHttpRequest"),n&&this._request.setRequestHeader("Content-type",n),this._request}}class g{static isTouchDevice(){return"ontouchstart"in document.documentElement}static isIOSDevice(){return g.isIPhoneDevice()||g.isIPadDevice()}static isNativeWindowsBrowser(){return g.isIEBrowser()||g.isEdgeBrowser()}static isIPhoneDevice(){return!!navigator.userAgent.match(/iPhone/i)}static isIPadDevice(){return!!navigator.userAgent.match(/iPad/i)}static isIEBrowser(){return-1!==navigator.userAgent.toLowerCase().indexOf("msie")||!!navigator.userAgent.match(/Trident.*rv:\d+\./)}static isEdgeBrowser(){return!!navigator.userAgent.match(/Edge\/\d+/i)}static getList(){return{"is-touch":g.isTouchDevice(),"is-ios":g.isIOSDevice(),"is-native-windows":g.isNativeWindowsBrowser(),"is-iphone":g.isIPhoneDevice(),"is-ipad":g.isIPadDevice(),"is-ie":g.isIEBrowser(),"is-edge":g.isEdgeBrowser()}}}const P="modal-backdrop",b="modal-backdrop-open",_="no-scroll";class v{constructor(){return v.instance||(v.instance=this),v.instance}create(e){this._removeExistingBackdrops(),document.body.insertAdjacentHTML("beforeend",this._getTemplate());const t=document.body.lastChild;document.documentElement.classList.add(_),setTimeout((function(){t.classList.add(b),"function"==typeof e&&e()}),75),this._dispatchEvents()}remove(e=350){const t=this._getBackdrops();c.iterate(t,(e=>e.classList.remove(b))),setTimeout(this._removeExistingBackdrops.bind(this),e),document.documentElement.classList.remove(_)}_dispatchEvents(){const e=g.isTouchDevice()?"touchstart":"click";document.addEventListener(e,(function(e){e.target.classList.contains(P)&&document.dispatchEvent(new CustomEvent("backdrop/onclick"))}))}_getBackdrops(){return document.querySelectorAll(`.${P}`)}_removeExistingBackdrops(){if(!1===this._exists())return;const e=this._getBackdrops();c.iterate(e,(e=>e.remove()))}_exists(){return document.querySelectorAll(`.${P}`).length>0}_getTemplate(){return`<div class="${P}"></div>`}}const S=Object.freeze(new v);class z{static create(e=null){S.create(e)}static remove(e=350){S.remove(e)}static SELECTOR_CLASS(){return P}}const I=Object.freeze(new class extends h{constructor(){super(document.body)}create(e=!0){!this.exists()&&e&&(z.create(),document.querySelector(`.${z.SELECTOR_CLASS()}`).insertAdjacentHTML("beforeend",h.getTemplate()))}remove(e=!0){super.remove(),e&&z.remove()}});class B{static create(e=!0){I.create(e)}static remove(e=!0){I.remove(e)}}window.PluginManager.register("UnzerPaymentBase",class extends u{static options={publicKey:null,shopLocale:null,submitButtonId:"confirmFormSubmit",disabledClass:"disabled",resourceIdElementId:"unzerResourceId",confirmFormId:"confirmOrderForm",errorWrapperClass:"unzer-payment--error-wrapper",errorContentSelector:".unzer-payment--error-wrapper .alert-content",errorShouldNotBeEmpty:"%field% should not be empty",isOrderEdit:!1};static submitting=!1;static unzerInstance=null;init(){this._registerElements(),this._registerEvents()}_registerElements(){let e=null;null!==this.options.shopLocale&&(e={locale:this.options.shopLocale}),this.unzerInstance=new window.unzer(this.options.publicKey,e),this.options.isOrderEdit?this.submitButton=document.getElementById(this.options.confirmFormId).getElementsByTagName("button")[0]:this.submitButton=document.getElementById(this.options.submitButtonId),this.confirmForm=document.getElementById(this.options.confirmFormId)}_registerEvents(){this.submitButton.addEventListener("click",this._onSubmitButtonClick.bind(this))}setSubmitButtonActive(e){e?(this.submitButton.classList.remove(this.options.disabledClass),this.submitButton.disabled=!1):(this.submitButton.classList.add(this.options.disabledClass),this.submitButton.disabled=!0)}submitResource(e){document.getElementById(this.options.resourceIdElementId).value=e.id,this.setSubmitButtonActive(!0),this.submitButton.click()}submitTypeId(e){document.getElementById(this.options.resourceIdElementId).value=e,this.setSubmitButtonActive(!0),this.submitButton.click(),this.setSubmitButtonActive(!1)}showError(e,t=!1){const n=document.getElementsByClassName(this.options.errorWrapperClass).item(0),i=document.querySelectorAll(this.options.errorContentSelector)[0];t&&""!==i.innerText?i.innerText=`${i.innerText}\n${e.message}`:i.innerText=e.message,n.hidden=!1,n.scrollIntoView({block:"end",behavior:"smooth"}),this.setSubmitButtonActive(!0),this.submitting=!1}renderErrorToElement(e,t){const n=document.getElementsByClassName(this.options.errorWrapperClass).item(0),i=document.querySelectorAll(this.options.errorContentSelector)[0];n.hidden=!1,i.innerText=e.message,t.appendChild(n)}_onSubmitButtonClick(e){if(!0!==this.submitting){if(this.submitting=!0,e.preventDefault(),!this._validateForm())return this.submitting=!1,void this.setSubmitButtonActive(!0);this.setSubmitButtonActive(!1),this.$emitter.publish("unzerBase_createResource")}}_validateForm(){let e=!0;const t=document.forms[this.options.confirmFormId].elements;this._clearErrorMessage();for(let n=0;n<t.length;n++){const i=t[n];if(!i.checkValidity())return i.dataset.customError&&this.showError({message:i.dataset.customError}),i.classList.add("is-invalid"),!1;i.required&&""===i.value?(i.classList.add("is-invalid"),0===i.labels.length&&e?i.scrollIntoView({block:"end",behavior:"smooth"}):i.labels.length>0&&this.showError({message:this.options.errorShouldNotBeEmpty.replace(/%field%/,i.labels[0].innerText)},!0),e=!1):i.classList.remove("is-invalid")}return e}_clearErrorMessage(){const e=document.getElementsByClassName(this.options.errorWrapperClass).item(0),t=document.querySelectorAll(this.options.errorContentSelector)[0];e.hidden=!0,t.innerText=""}getB2bCustomerObject(e){const t=`${e.firstName} ${e.lastName}`,n=e.birthday?new Date(e.birthday):null,i={firstname:e.firstName,lastname:e.lastName,email:e.email,company:e.activeBillingAddress.company,salutation:e.salutation.salutationKey,billingAddress:{name:t,street:e.activeBillingAddress.street,zip:e.activeBillingAddress.zipcode,city:e.activeBillingAddress.city,country:e.activeBillingAddress.country.iso},shippingAddress:{name:t,street:e.activeShippingAddress.street,zip:e.activeShippingAddress.zipcode,city:e.activeShippingAddress.city,country:e.activeShippingAddress.country.iso}};return n&&(i.birthDate=n.getFullYear()+"-"+(n.getMonth()+1).toString().padStart(2,"0")+"-"+n.getDay().toString().padStart(2,"0")),i}},"[data-unzer-payment-base]"),window.PluginManager.register("UnzerPaymentCreditCard",class extends u{static options={numberFieldId:"unzer-payment-credit-card-number",holderFieldId:"unzer-payment-credit-card-holder",numberFieldInputId:"unzer-payment-credit-card-number-input",expiryFieldId:"unzer-payment-credit-card-expiry",cvcFieldId:"unzer-payment-credit-card-cvc",iconFieldId:"unzer-payment-credit-card-icon",invalidClass:"is-invalid",elementWrapperSelector:".unzer-payment-credit-card-wrapper-elements",radioButtonSelector:'*[name="savedCreditCard"]',radioButtonNewId:"card-new",selectedRadioButtonSelector:'*[name="savedCreditCard"]:checked',hasSavedCards:!1,placeholderBrandImageUrl:"https://static.unzer.com/assets/images/common/group-5.svg"};static creditCard;static submitting=!1;static _unzerPaymentPlugin=null;static cvcValid=!1;static numberValid=!1;static expiryValid=!1;static holderValid=!1;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this._createForm(),this._registerEvents(),this.options.hasSavedCards?a.querySelector(this.el,this.options.elementWrapperSelector).hidden=!0:this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_createForm(){this.creditCard=this._unzerPaymentPlugin.unzerInstance.Card(),this.creditCard.create("number",{containerId:this.options.numberFieldInputId,onlyIframe:!0}),this.creditCard.create("holder",{containerId:this.options.holderFieldId,onlyIframe:!0}),this.creditCard.create("expiry",{containerId:this.options.expiryFieldId,onlyIframe:!0}),this.creditCard.create("cvc",{containerId:this.options.cvcFieldId,onlyIframe:!0}),this.creditCard.addEventListener("change",this._onChangeForm.bind(this))}_registerEvents(){if(this.options.hasSavedCards){const e=a.querySelectorAll(this.el,this.options.radioButtonSelector);for(let t=0;t<e.length;t++)e[t].addEventListener("change",(e=>this._onRadioButtonChange(e)))}this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this})}_onRadioButtonChange(e){const t=e.target;a.querySelector(this.el,this.options.elementWrapperSelector).hidden=t.id!==this.options.radioButtonNewId,t.id===this.options.radioButtonNewId?this._unzerPaymentPlugin.setSubmitButtonActive(!0===this.cvcValid&&!0===this.numberValid&&!0===this.expiryValid):this._unzerPaymentPlugin.setSubmitButtonActive(!0)}_onChangeForm(e){if(e.cardType){let t=this.options.placeholderBrandImageUrl;return"unknown"!==e.cardType.type&&(t=this._getBrandImageUrl(e.cardType.type)),void(document.getElementById(this.options.iconFieldId).src=t)}if(!e.type||this.submitting)return;const t=this._getInputElementByEvent(e),n=this._getErrorElementByEvent(e);if(!1===e.success?(t.classList.add(this.options.invalidClass),n.hidden=!1):!0===e.success&&(t.classList.remove(this.options.invalidClass),n.hidden=!0),e.error&&(n.getElementsByClassName("unzer-error-message")[0].innerText=e.error),"cvc"===e.type?this.cvcValid=e.success:"number"===e.type?this.numberValid=e.success:"expiry"===e.type?this.expiryValid=e.success:"holder"===e.type&&(this.holderValid=e.success),this.options.hasSavedCards){const e=a.querySelector(this.el,this.options.selectedRadioButtonSelector);if(e&&e.id!==this.options.radioButtonNewId)return void this._unzerPaymentPlugin.setSubmitButtonActive(!0)}this._unzerPaymentPlugin.setSubmitButtonActive(!0===this.cvcValid&&!0===this.numberValid&&!0===this.expiryValid&&!0===this.holderValid)}_onCreateResource(){let e=null;this.options.hasSavedCards&&(e=a.querySelector(this.el,this.options.selectedRadioButtonSelector)),this.submitting=!0,this._unzerPaymentPlugin.setSubmitButtonActive(!1),null===e||e.id===this.options.radioButtonNewId?this.creditCard.createResource().then((e=>this._submitPayment(e))).catch((e=>this._handleError(e))):this._unzerPaymentPlugin.submitTypeId(e.value)}_getInputElementByEvent(e){const t=`#unzer-payment-credit-card-${e.type}`;return a.querySelector(this.el,t)}_getErrorElementByEvent(e){const t=`#unzer-payment-credit-card-${e.type}-error`;return a.querySelector(this.el,t)}_submitPayment(e){this._unzerPaymentPlugin.submitResource(e)}_handleError(e){this._unzerPaymentPlugin.showError(e)}_getBrandImageUrl(e){return`https://static.unzer.com/assets/images/brands/${e}.svg`}},"[data-unzer-payment-credit-card]"),window.PluginManager.register("UnzerPaymentIdeal",class extends u{static ideal;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.ideal=this._unzerPaymentPlugin.unzerInstance.Ideal(),this._createForm(),this._registerEvents()}_createForm(){this.ideal.create("ideal",{containerId:"unzer-payment-ideal-container"}),this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_registerEvents(){this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this}),this.ideal&&this.ideal.addEventListener("change",(e=>this._onFormChange(e)),{scope:this})}_onFormChange(e){e.value&&this._unzerPaymentPlugin.setSubmitButtonActive(!0)}_onCreateResource(){this._unzerPaymentPlugin.setSubmitButtonActive(!1),this.ideal.createResource().then((e=>this._unzerPaymentPlugin.submitResource(e))).catch((e=>this._unzerPaymentPlugin.showError(e)))}},"[data-unzer-payment-ideal]"),window.PluginManager.register("UnzerPaymentInvoice",class extends u{static options={unzerPaymentCardId:"unzer-payment-card"};static invoice;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.invoice=this._unzerPaymentPlugin.unzerInstance.Invoice(),this._registerEvents()}_registerEvents(){this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this})}_onCreateResource(){this._unzerPaymentPlugin.setSubmitButtonActive(!1),this.invoice.createResource().then((e=>this._submitPayment(e))).catch((e=>this._handleError(e)))}_submitPayment(e){this._unzerPaymentPlugin.submitResource(e)}_handleError(e){this._unzerPaymentPlugin.showError(e)}},"[data-unzer-payment-invoice]"),window.PluginManager.register("UnzerPaymentInvoiceSecured",class extends u{static options={isB2BCustomer:!1,customerInfo:null};static _unzerPaymentPlugin=null;static invoiceSecured=null;static b2bCustomerProvider=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.invoiceSecured=this._unzerPaymentPlugin.unzerInstance.InvoiceSecured(),this.options.isB2BCustomer&&this._createB2bForm(),this._registerEvents()}_registerEvents(){this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this})}_createB2bForm(){this.b2bCustomerProvider=this._unzerPaymentPlugin.unzerInstance.B2BCustomer(),this.b2bCustomerProvider.b2bCustomerEventHandler=e=>this._onValidateB2bForm(e),this.b2bCustomerProvider.initFormFields(this._unzerPaymentPlugin.getB2bCustomerObject(this.options.customerInfo)),this.b2bCustomerProvider.create({containerId:"unzer-payment-b2b-form",externalCustomerId:this.options.customerInfo.customerNumber})}_onValidateB2bForm(e){this._unzerPaymentPlugin.setSubmitButtonActive(e.success)}_onCreateResource(){this._unzerPaymentPlugin.setSubmitButtonActive(!1),this.options.isB2BCustomer?this.b2bCustomerProvider.createCustomer().then((e=>this._onB2bCustomerCreated(e.id))).catch((e=>this._handleError(e))):this.invoiceSecured.createResource().then((e=>this._submitPayment(e))).catch((e=>this._handleError(e)))}_onB2bCustomerCreated(e){document.getElementById("unzerCustomerId").value=e,this.invoiceSecured.createResource().then((e=>this._submitPayment(e))).catch((e=>this._handleError(e)))}_submitPayment(e){this._unzerPaymentPlugin.submitResource(e)}_handleError(e){this._unzerPaymentPlugin.showError(e)}},"[data-unzer-payment-invoice-secured]"),window.PluginManager.register("UnzerPaymentInstallmentSecured",class extends u{static options={installmentSecuredAmount:0,installmentSecuredCurrency:"",installmentSecuredOrderDate:"",installmentsTotalValueElementId:"unzer-payment-installments-total",installmentsInterestValueElementId:"unzer-payment-installments-interest",formLoadingIndicatorElementId:"element-loader",currencyIso:"EUR",currencyFormatLocale:"en-GB",starSymbol:"*",birthdateInputIdSelector:"unzerPaymentBirthday",birthdateContainerIdSelector:"unzerPaymentBirthdayContainer"};static installmentSecured;static birthdateContainer;static birthdateInput;static unzerInputsValid;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.installmentSecured=this._unzerPaymentPlugin.unzerInstance.InstallmentSecured(),this._unzerPaymentPlugin.setSubmitButtonActive(!1),this.birthdateContainer=document.getElementById(this.options.birthdateContainerIdSelector),this.birthdateInput=document.getElementById(this.options.birthdateInputIdSelector),this.unzerInputsValid=!1,this._createForm(),this._registerEvents()}_createForm(){const e=document.getElementById(this.options.formLoadingIndicatorElementId);p.create(e),this.installmentSecured.create({containerId:"unzer-payment-installment-secured-container",amount:this.options.installmentSecuredAmount.toFixed(4),currency:this.options.installmentSecuredCurrency,orderDate:this.options.installmentSecuredOrderDate}).then((()=>{e.hidden=!0})).catch((t=>{this._unzerPaymentPlugin.renderErrorToElement(t,e),this._unzerPaymentPlugin.setSubmitButtonActive(!1)})).finally((()=>{p.remove(e)}))}_registerEvents(){this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this}),this.installmentSecured.addEventListener("installmentSecuredEvent",(e=>this._onChangeInstallmentSecuredForm(e))),this.birthdateInput.addEventListener("change",this._onBirthdateInputChange.bind(this))}_onCreateResource(){this._unzerPaymentPlugin.setSubmitButtonActive(!1),this.installmentSecured.createResource().then((e=>this._unzerPaymentPlugin.submitResource(e))).catch((e=>this._unzerPaymentPlugin.showError(e)))}_onChangeInstallmentSecuredForm(e){if("validate"===e.action&&(this.unzerInputsValid=e.success,e.success&&this._validateBirthdate()?this._unzerPaymentPlugin.setSubmitButtonActive(!0):this._unzerPaymentPlugin.setSubmitButtonActive(!1)),"plan-detail"===e.currentStep){const e=document.getElementById(this.options.installmentsTotalValueElementId),t=document.getElementById(this.options.installmentsInterestValueElementId);e.innerText=this._formatCurrency(this.installmentSecured.selectedInstallmentPlan.totalAmount)+this.options.starSymbol,t.innerText=this._formatCurrency(this.installmentSecured.selectedInstallmentPlan.totalInterestAmount)+this.options.starSymbol}}_formatCurrency(e){return e.toLocaleString(this.options.currencyFormatLocale,{style:"currency",currency:this.options.currencyIso})}_onBirthdateInputChange(){this._validateBirthdate()&&this.unzerInputsValid?this._unzerPaymentPlugin.setSubmitButtonActive(!0):this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_validateBirthdate(){if(""===this.birthdateInput.value)return!1;const e=new Date(this.birthdateInput.value),t=new Date,n=new Date;e.setHours(0,0,0,0),t.setHours(0,0,0,0),n.setHours(0,0,0,0),t.setDate(t.getDate()+1),n.setFullYear(n.getFullYear()-18);const i=e<=n&&e<t;return i?this.birthdateContainer.classList.remove("error"):this.birthdateContainer.classList.add("error"),i}},"[data-unzer-payment-installment-secured]"),window.PluginManager.register("UnzerPaymentPayPal",class extends u{static options={radioButtonSelector:'input[name="savedPayPalAccount"]',selectedRadioButtonSelector:'input[name="savedPayPalAccount"]:checked',radioButtonNewId:"account-new",elementWrapperSelector:".unzer-payment-saved-accounts-wrapper-elements",hasSavedAccounts:!1};static submitting=!1;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this._registerEvents(),this.options.hasSavedAccounts&&(a.querySelector(this.el,this.options.elementWrapperSelector).hidden=!0)}_registerEvents(){if(this.options.hasSavedAccounts){const e=a.querySelectorAll(this.el,this.options.radioButtonSelector);for(let t=0;t<e.length;t++)e[t].addEventListener("change",(e=>this._onRadioButtonChange(e)))}this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()))}_onRadioButtonChange(e){const t=e.target;a.querySelector(this.el,this.options.elementWrapperSelector).hidden=t.id!==this.options.radioButtonNewId}_onCreateResource(){const e=document.querySelector(this.options.selectedRadioButtonSelector);null===e||e.id===this.options.radioButtonNewId?this._unzerPaymentPlugin.confirmForm.submit():this._unzerPaymentPlugin.submitTypeId(e.value)}},"[data-unzer-payment-paypal]"),window.PluginManager.register("UnzerPaymentSepaDirectDebit",class extends u{static options={acceptMandateId:"acceptSepaMandate",elementWrapperSelector:".unzer-payment-sepa-wrapper-elements",radioButtonSelector:'*[name="savedDirectDebitDevice"]',radioButtonNewAccountId:"device-new",selectedRadioButtonSelector:'*[name="savedDirectDebitDevice"]:checked',hasSepaDevices:!1};static sepa;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.sepa=this._unzerPaymentPlugin.unzerInstance.SepaDirectDebit(),this.mandateAcceptedCheckbox=document.getElementById(this.options.acceptMandateId),this._createForm(),this._registerEvents(),this.options.hasSepaDevices||this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_createForm(){this.sepa.create("sepa-direct-debit",{containerId:"unzer-payment-sepa-container"})}_registerEvents(){if(this.options.hasSepaDevices){const e=a.querySelectorAll(this.el,this.options.radioButtonSelector);for(let t=0;t<e.length;t++)e[t].addEventListener("change",(e=>this._onRadioButtonChange(e)));document.querySelector(this.options.selectedRadioButtonSelector).dispatchEvent(new Event("change"))}this.sepa.addEventListener("change",(e=>this._onFormChange(e))),this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this})}_onRadioButtonChange(e){const t=e.target;a.querySelector(this.el,this.options.elementWrapperSelector).hidden=t.id!==this.options.radioButtonNewAccountId,t&&t.id!==this.options.radioButtonNewAccountId?(this._unzerPaymentPlugin.setSubmitButtonActive(!0),this.mandateAcceptedCheckbox.required=!1):(this._unzerPaymentPlugin.setSubmitButtonActive(this.sepa.validated),this.mandateAcceptedCheckbox.required=!0)}_onFormChange(e){this._unzerPaymentPlugin.setSubmitButtonActive(e.success)}_onCreateResource(){const e=document.querySelector(this.options.selectedRadioButtonSelector);this._unzerPaymentPlugin.setSubmitButtonActive(!1),e&&e.id!==this.options.radioButtonNewAccountId?this._submitDevicePayment(e.value):this.sepa.createResource().then((e=>this._submitPayment(e))).catch((e=>this._handleError(e)))}_submitPayment(e){this._unzerPaymentPlugin.submitResource(e)}_submitDevicePayment(e){this._unzerPaymentPlugin.submitTypeId(e)}_handleError(e){this._unzerPaymentPlugin.showError(e)}},"[data-unzer-payment-sepa-direct-debit]"),window.PluginManager.register("UnzerPaymentSepaDirectDebitSecured",class extends u{static options={birthDateFieldId:"unzerPaymentBirthday",acceptMandateId:"acceptSepaMandate",elementWrapperSelector:".unzer-payment-sepa-wrapper-elements",radioButtonSelector:'*[name="savedDirectDebitDevice"]',radioButtonNewAccountId:"device-new",selectedRadioButtonSelector:'*[name="savedDirectDebitDevice"]:checked'};static sepa;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.sepa=this._unzerPaymentPlugin.unzerInstance.SepaDirectDebitSecured(),this.birthDateElement=document.getElementById(this.options.birthDateFieldId),this.mandateAcceptedCheckbox=document.getElementById(this.options.acceptMandateId),this._createForm(),this._registerEvents(),this.options.hasSepaDevices||this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_createForm(){this.sepa.create("sepa-direct-debit-secured",{containerId:"unzer-payment-sepa-container"})}_registerEvents(){if(this.options.hasSepaDevices){const e=a.querySelectorAll(this.el,this.options.radioButtonSelector);for(let t=0;t<e.length;t++)e[t].addEventListener("change",(e=>this._onRadioButtonChange(e)));document.querySelector(this.options.selectedRadioButtonSelector).dispatchEvent(new Event("change"))}this.sepa.addEventListener("change",(e=>this._onFormChange(e))),this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this})}_onRadioButtonChange(e){const t=e.target;a.querySelector(this.el,this.options.elementWrapperSelector).hidden=t.id!==this.options.radioButtonNewAccountId,t&&t.id!==this.options.radioButtonNewAccountId?(this._unzerPaymentPlugin.setSubmitButtonActive(!0),this.birthDateElement.required=!1,this.mandateAcceptedCheckbox.required=!1):(this._unzerPaymentPlugin.setSubmitButtonActive(this.sepa.validated),this.birthDateElement.required=!0,this.mandateAcceptedCheckbox.required=!0)}_onFormChange(e){this._unzerPaymentPlugin.setSubmitButtonActive(e.success)}_onCreateResource(){const e=document.querySelector(this.options.selectedRadioButtonSelector);this._unzerPaymentPlugin.setSubmitButtonActive(!1),e&&e.id!==this.options.radioButtonNewAccountId?this._submitDevicePayment(e.value):this.sepa.createResource().then((e=>this._submitPayment(e))).catch((e=>this._handleError(e)))}_submitPayment(e){this._unzerPaymentPlugin.submitResource(e)}_submitDevicePayment(e){this._unzerPaymentPlugin.submitTypeId(e)}_handleError(e){this._unzerPaymentPlugin.showError(e)}},"[data-unzer-payment-sepa-direct-debit-secured]"),window.PluginManager.register("UnzerPaymentApplePay",class extends u{static options={countryCode:"DE",currency:"EUR",shopName:"Unzer GmbH",amount:"0.0",applePayButtonSelector:"apple-pay-button",checkoutConfirmButtonSelector:"#confirmFormSubmit",applePayMethodSelector:".unzer-payment-apple-pay-method-wrapper",authorizePaymentUrl:"",merchantValidationUrl:"",noApplePayMessage:"",supportedNetworks:["masterCard","visa"]};static submitting=!1;static _unzerPaymentPlugin=null;static applePay;static client;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.client=new y,this._hasCapability()?(this._createScript(),this._createForm(),this._registerEvents()):this._disableApplePay()}_hasCapability(){return window.ApplePaySession&&window.ApplePaySession.canMakePayments()&&window.ApplePaySession.supportsVersion(6)}_disableApplePay(){a.querySelector(document,this.options.applePayMethodSelector,!1).remove(),a.querySelectorAll(document,"[data-unzer-payment-apple-pay]",!1).forEach((e=>e.remove())),this._unzerPaymentPlugin.showError({message:this.options.noApplePayMessage}),this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_createScript(){const e=document.createElement("script");e.type="text/javascript",e.src="https://applepay.cdn-apple.com/jsapi/v1/apple-pay-sdk.js",document.head.appendChild(e)}_createForm(){this.applePay=this._unzerPaymentPlugin.unzerInstance.ApplePay(),a.querySelector(document,this.options.checkoutConfirmButtonSelector).style.display="none"}_startPayment(){if(!this._unzerPaymentPlugin._validateForm())return;const e=this,t={countryCode:this.options.countryCode,currencyCode:this.options.currency,supportedNetworks:this.options.supportedNetworks,merchantCapabilities:["supports3DS"],total:{label:this.options.shopName,amount:this.options.amount}};if(!window.ApplePaySession)return;const n=new window.ApplePaySession(6,t);n.onvalidatemerchant=t=>{try{e.client.post(e.options.merchantValidationUrl,JSON.stringify({merchantValidationUrl:t.validationURL}),(e=>{n.completeMerchantValidation(JSON.parse(e))}))}catch(e){n.abort()}},n.onpaymentauthorized=t=>{const i=t.payment.token.paymentData;e.applePay.createResource(i).then((t=>{B.create();try{e.client.post(e.options.authorizePaymentUrl,JSON.stringify(t),(i=>{"pending"===JSON.parse(i).transactionStatus?(n.completePayment({status:window.ApplePaySession.STATUS_SUCCESS}),e._unzerPaymentPlugin.setSubmitButtonActive(!1),e._unzerPaymentPlugin.submitting=!0,e._unzerPaymentPlugin.submitResource(t)):(B.remove(),n.completePayment({status:window.ApplePaySession.STATUS_FAILURE}),n.abort())}))}catch(e){B.remove(),n.completePayment({status:window.ApplePaySession.STATUS_FAILURE}),n.abort()}})).catch((()=>{B.remove(),n.completePayment({status:window.ApplePaySession.STATUS_FAILURE}),n.abort()})).finally((()=>{e._unzerPaymentPlugin.setSubmitButtonActive(!0),e._unzerPaymentPlugin.submitting=!1}))},n.begin()}_registerEvents(){a.querySelector(document,this.options.applePayButtonSelector).addEventListener("click",this._startPayment.bind(this))}_handleError(e){this._unzerPaymentPlugin.showError(e)}},"[data-unzer-payment-apple-pay]"),window.PluginManager.register("UnzerPaymentApplePayV2",class extends u{static options={countryCode:"DE",currency:"EUR",shopName:"Unzer GmbH",amount:"0.0",applePayButtonSelector:".apple-pay-button",checkoutConfirmButtonSelector:"#confirmFormSubmit",applePayMethodSelector:".unzer-payment-apple-pay-v2-method-wrapper",authorizePaymentUrl:"",merchantValidationUrl:"",noApplePayMessage:"",supportedNetworks:["masterCard","visa"]};static submitting=!1;static _unzerPaymentPlugin=null;static applePay;static client;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.client=new y,this._hasCapability()?(this._createForm(),this._registerEvents()):this._disableApplePay()}_hasCapability(){return window.ApplePaySession&&window.ApplePaySession.canMakePayments()&&window.ApplePaySession.supportsVersion(6)}_disableApplePay(){a.querySelector(document,this.options.applePayMethodSelector,!1).remove(),a.querySelectorAll(document,"[data-unzer-payment-apple-pay-v2]",!1).forEach((e=>e.remove())),this._unzerPaymentPlugin.showError({message:this.options.noApplePayMessage}),this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_createForm(){this.applePay=this._unzerPaymentPlugin.unzerInstance.ApplePay(),a.querySelector(document,this.options.checkoutConfirmButtonSelector).style.display="none"}_startPayment(){if(!this._unzerPaymentPlugin._validateForm())return;const e=this,t={countryCode:this.options.countryCode,currencyCode:this.options.currency,supportedNetworks:this.options.supportedNetworks,merchantCapabilities:this.options.merchantCapabilities,total:{label:this.options.shopName,amount:this.options.amount}};if(!window.ApplePaySession)return;const n=this.applePay.initApplePaySession(t);n.onpaymentauthorized=t=>{const i=t.payment.token.paymentData;e.applePay.createResource(i).then((t=>{e._unzerPaymentPlugin.setSubmitButtonActive(!1),e._unzerPaymentPlugin.submitting=!0,e._unzerPaymentPlugin.submitResource(t)})).catch((()=>{B.remove(),n.abort()})).finally((()=>{e._unzerPaymentPlugin.setSubmitButtonActive(!0),e._unzerPaymentPlugin.submitting=!1}))},n.begin()}_registerEvents(){a.querySelector(document,this.options.applePayButtonSelector).addEventListener("click",this._startPayment.bind(this))}_handleError(e){this._unzerPaymentPlugin.showError(e)}},"[data-unzer-payment-apple-pay-v2]"),window.PluginManager.register("UnzerPaymentPaylaterInvoice",class extends u{static options={isB2BCustomer:!1};static paylaterInvoice;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.paylaterInvoice=this._unzerPaymentPlugin.unzerInstance.PaylaterInvoice(),this.paylaterInvoice.create({containerId:"unzer-payment-paylater-invoice-wrapper",customerType:this.options.isB2BCustomer?"B2B":"B2C"}),this._registerEvents()}_registerEvents(){this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this})}_onCreateResource(){this._unzerPaymentPlugin.setSubmitButtonActive(!1),this._createResource()}_createResource(){this.paylaterInvoice.createResource().then((e=>this._submitPayment(e))).catch((e=>this._handleError(e)))}_submitPayment(e){this._unzerPaymentPlugin.submitResource(e)}_handleError(e){this._unzerPaymentPlugin.showError(e)}},"[data-unzer-payment-paylater-invoice]"),window.PluginManager.register("UnzerPaymentPaylaterInstallment",class extends u{static options={formLoadingIndicatorElementId:"element-loader",birthdateInputIdSelector:"unzerPaymentBirthday",birthdateContainerIdSelector:"unzerPaymentBirthdayContainer",paylaterInstallmentAmount:0,paylaterInstallmentCurrency:"",currencyIso:"EUR",countryIso:"DE",threatMetrixId:""};static paylaterInstallment;static birthdateContainer;static birthdateInput;static unzerInputsValid;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.paylaterInstallment=this._unzerPaymentPlugin.unzerInstance.PaylaterInstallment(),this._unzerPaymentPlugin.setSubmitButtonActive(!1),this.birthdateContainer=document.getElementById(this.options.birthdateContainerIdSelector),this.birthdateInput=document.getElementById(this.options.birthdateInputIdSelector),this.unzerInputsValid=!1,this._createForm(),this._registerEvents()}_createForm(){const e=document.getElementById(this.options.formLoadingIndicatorElementId);p.create(e),this.paylaterInstallment.create({containerId:"unzer-payment-paylater-installment-container",amount:this.options.paylaterInstallmentAmount.toFixed(4),currency:this.options.paylaterInstallmentCurrency,country:this.options.countryIso,threatMetrixId:this.options.threatMetrixId}).then((()=>{e.hidden=!0})).catch((t=>{this._unzerPaymentPlugin.renderErrorToElement(t,e),this._unzerPaymentPlugin.setSubmitButtonActive(!1)})).finally((()=>{p.remove(e)}))}_registerEvents(){this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this}),this.paylaterInstallment.addEventListener("paylaterInstallmentEvent",(e=>this._onChangeInstallmentSecuredForm(e))),this.birthdateInput.addEventListener("change",this._onBirthdateInputChange.bind(this))}_onCreateResource(){this._unzerPaymentPlugin.setSubmitButtonActive(!1),this.paylaterInstallment.createResource().then((e=>this._unzerPaymentPlugin.submitResource(e))).catch((e=>this._unzerPaymentPlugin.showError(e)))}_onChangeInstallmentSecuredForm(e){switch("validate"===e.action&&(this.unzerInputsValid=e.success,e.success&&this._validateBirthdate()?this._unzerPaymentPlugin.setSubmitButtonActive(!0):this._unzerPaymentPlugin.setSubmitButtonActive(!1)),e.currentStep){case"plan-list":this._unzerPaymentPlugin.setSubmitButtonActive(!1);break;case"plan-detail":this._unzerPaymentPlugin.setSubmitButtonActive(!0)}}_formatCurrency(e){return e.toLocaleString(this.options.currencyFormatLocale,{style:"currency",currency:this.options.currencyIso})}_onBirthdateInputChange(){this._validateBirthdate()&&this.unzerInputsValid?this._unzerPaymentPlugin.setSubmitButtonActive(!0):this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_validateBirthdate(){if(""===this.birthdateInput.value)return!1;const e=new Date(this.birthdateInput.value),t=new Date,n=new Date;e.setHours(0,0,0,0),t.setHours(0,0,0,0),n.setHours(0,0,0,0),t.setDate(t.getDate()+1),n.setFullYear(n.getFullYear()-18);const i=e<=n&&e<t;return i?this.birthdateContainer.classList.remove("error"):this.birthdateContainer.classList.add("error"),i}},"[data-unzer-payment-paylater-installment]"),window.PluginManager.register("UnzerPaymentPaylaterDirectDebitSecured",class extends u{static options={formLoadingIndicatorElementId:"element-loader",birthdateInputIdSelector:"unzerPaymentBirthday",birthdateContainerIdSelector:"unzerPaymentBirthdayContainer",paylaterDirectDebitSecuredAmount:0,paylaterDirectDebitSecuredCurrency:"",currencyIso:"EUR",countryIso:"DE",threatMetrixId:""};static paylaterDirectDebitSecured;static birthdateContainer;static birthdateInput;static unzerInputsValid;static _unzerPaymentPlugin=null;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.paylaterDirectDebitSecured=this._unzerPaymentPlugin.unzerInstance.PaylaterDirectDebit(),this._unzerPaymentPlugin.setSubmitButtonActive(!1),this.birthdateContainer=document.getElementById(this.options.birthdateContainerIdSelector),this.birthdateInput=document.getElementById(this.options.birthdateInputIdSelector),this.unzerInputsValid=!1,this._createForm(),this._registerEvents()}_createForm(){this.paylaterDirectDebitSecured.create("paylater-direct-debit",{containerId:"unzer-payment-paylater-direct-debit-secured-container",amount:this.options.paylaterDirectDebitSecuredAmount.toFixed(4),currency:this.options.paylaterDirectDebitSecuredCurrency,country:this.options.countryIso,threatMetrixId:this.options.threatMetrixId})}_registerEvents(){this._unzerPaymentPlugin.$emitter.subscribe("unzerBase_createResource",(()=>this._onCreateResource()),{scope:this}),this.paylaterDirectDebitSecured.sepaEventHandler=this._handleSepaDataChange.bind(this),this.birthdateInput.addEventListener("change",this._onBirthdateInputChange.bind(this)),""!==this.birthdateInput.value&&this._onBirthdateInputChange()}_handleSepaDataChange(e){this.unzerInputsValid=this.paylaterDirectDebitSecured.isHolderValidated&&this.paylaterDirectDebitSecured.isIbanValidated,this._unzerPaymentPlugin.setSubmitButtonActive(this.unzerInputsValid&&this._validateBirthdate())}_onCreateResource(){this._unzerPaymentPlugin.setSubmitButtonActive(!1);const e=document.getElementById(this.options.formLoadingIndicatorElementId);p.create(e),this.paylaterDirectDebitSecured.createResource().then(function(e){this._submitPayment(e)}.bind(this)).catch(function(t){this._unzerPaymentPlugin.renderErrorToElement(t,e),p.remove(e)}.bind(this))}_submitPayment(e){this._unzerPaymentPlugin.submitResource(e)}_formatCurrency(e){return e.toLocaleString(this.options.currencyFormatLocale,{style:"currency",currency:this.options.currencyIso})}_onBirthdateInputChange(){this._validateBirthdate()&&this.unzerInputsValid?this._unzerPaymentPlugin.setSubmitButtonActive(!0):this._unzerPaymentPlugin.setSubmitButtonActive(!1)}_validateBirthdate(){if(""===this.birthdateInput.value)return!1;const e=new Date(this.birthdateInput.value),t=new Date,n=new Date;e.setHours(0,0,0,0),t.setHours(0,0,0,0),n.setHours(0,0,0,0),t.setDate(t.getDate()+1),n.setFullYear(n.getFullYear()-18);const i=e<=n&&e<t;return i?this.birthdateContainer.classList.remove("error"):this.birthdateContainer.classList.add("error"),i}},"[data-unzer-payment-paylater-direct-debit-secured]"),window.PluginManager.register("UnzerPaymentGooglePay",class extends u{static options={googlePayButtonId:"unzer-google-pay-button",checkoutConfirmButtonSelector:"#confirmFormSubmit",merchantName:"",merchantId:"",gatewayMerchantId:"",currency:"EUR",amount:"0.0",countryCode:"DE",allowedCardNetworks:[],allowCreditCards:!0,allowPrepaidCards:!0,buttonColor:"default",buttonSizeMode:"fill"};static submitting=!1;static _unzerPaymentPlugin=null;static client;init(){this._unzerPaymentPlugin=window.PluginManager.getPluginInstances("UnzerPaymentBase")[0],this.client=new y,this.googlePayInstance=this._unzerPaymentPlugin.unzerInstance.Googlepay(),this._createScript((()=>{this._registerGooglePayButton()})),this._hideBuyButton()}_registerGooglePayButton(){const e=this,t=this.googlePayInstance.initPaymentDataRequestObject({gatewayMerchantId:this.options.gatewayMerchantId,merchantInfo:{merchantName:this.options.merchantName,merchantId:this.options.merchantId},transactionInfo:{currencyCode:this.options.currency,countryCode:this.options.countryCode,totalPriceStatus:"ESTIMATED",totalPrice:String(this.options.amount)},buttonOptions:{buttonColor:this.options.buttonColor,buttonSizeMode:this.options.buttonSizeMode},allowedCardNetworks:this.options.allowedCardNetworks,allowCreditCards:this.options.allowCreditCards,allowPrepaidCards:this.options.allowPrepaidCards,onPaymentAuthorizedCallback:t=>{const n=document.getElementById(e.options.googlePayButtonId);return n.style.display="none",e.googlePayInstance.createResource(t).then((t=>(!1!==e._unzerPaymentPlugin._validateForm()?(e._unzerPaymentPlugin.submitting=!0,e._unzerPaymentPlugin.submitResource(t)):n.style.display="",{status:"success"}))).catch((t=>{n.style.display="";const i=t;return i.message=t.customerMessage||t.message||"Error",e._handleError(i),{status:"error",message:i.message||"Unexpected error"}}))}});this.googlePayInstance.create({containerId:e.options.googlePayButtonId},t)}_createScript(e){const t=document.createElement("script");t.type="text/javascript",t.src="https://pay.google.com/gp/p/js/pay.js",t.onload=e,document.head.appendChild(t)}_hideBuyButton(){a.querySelector(document,this.options.checkoutConfirmButtonSelector).style.display="none"}_handleError(e){this._unzerPaymentPlugin.showError(e)}},"[data-unzer-payment-google-pay]")})();